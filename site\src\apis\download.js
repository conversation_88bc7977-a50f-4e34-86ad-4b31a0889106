/**
 * 下载资料相关API接口
 */
import { DOWNLOAD_ENDPOINTS, DOWNLOAD_CONFIG } from '~/config/api'

/**
 * 获取资料列表
 * @param {Object} params 查询参数
 * @param {string} params.category 分类
 * @param {number} params.page 页码
 * @param {number} params.limit 每页数量
 * @returns {Promise} 资料列表数据
 */
export function getDownloadList(params = {}) {
  return useMyFetch(DOWNLOAD_ENDPOINTS.LIST, {
    method: 'GET',
    query: params,
  });
}

/**
 * 获取资料详情
 * @param {number} id 资料ID
 * @returns {Promise} 资料详情数据
 */
export function getDownloadDetail(id) {
  return useMyFetch(`${DOWNLOAD_ENDPOINTS.DETAIL}/${id}`, {
    method: 'GET',
  });
}

/**
 * 下载资料
 * @param {number} id 资料ID
 * @returns {Promise} 下载链接
 */
export function downloadFile(id) {
  return useMyFetch(`${DOWNLOAD_ENDPOINTS.DOWNLOAD}/${id}`, {
    method: 'POST',
  });
}

/**
 * 获取资料分类
 * @returns {Promise} 分类列表
 */
export function getDownloadCategories() {
  return useMyFetch(DOWNLOAD_ENDPOINTS.CATEGORIES, {
    method: 'GET',
  });
}

/**
 * 格式化文件大小
 * @param {number} bytes 文件大小（字节）
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
} 