package services

import (
	"bbs-go/internal/models"
	"bbs-go/internal/repositories"
	"time"

	"github.com/mlogclub/simple/sqls"
	"github.com/mlogclub/simple/web/params"
)

var DownloadService = newDownloadService()

func newDownloadService() *downloadService {
	return &downloadService{}
}

type downloadService struct {
}

func (s *downloadService) Get(id int64) *models.Download {
	return repositories.DownloadRepository.Get(sqls.DB(), id)
}

func (s *downloadService) Take(where ...interface{}) *models.Download {
	return repositories.DownloadRepository.Take(sqls.DB(), where...)
}

func (s *downloadService) Find(cnd *sqls.Cnd) []models.Download {
	return repositories.DownloadRepository.Find(sqls.DB(), cnd)
}

func (s *downloadService) FindOne(cnd *sqls.Cnd) *models.Download {
	return repositories.DownloadRepository.FindOne(sqls.DB(), cnd)
}

func (s *downloadService) FindPageByParams(params *params.QueryParams) (list []models.Download, paging *sqls.Paging) {
	return repositories.DownloadRepository.FindPageByParams(sqls.DB(), params)
}

func (s *downloadService) FindPageByCnd(cnd *sqls.Cnd) (list []models.Download, paging *sqls.Paging) {
	return repositories.DownloadRepository.FindPageByCnd(sqls.DB(), cnd)
}

func (s *downloadService) Count(cnd *sqls.Cnd) int64 {
	return repositories.DownloadRepository.Count(sqls.DB(), cnd)
}

func (s *downloadService) Create(t *models.Download) error {
	t.CreateTime = time.Now()
	t.UpdateTime = time.Now()
	return repositories.DownloadRepository.Create(sqls.DB(), t)
}

func (s *downloadService) Update(t *models.Download) error {
	t.UpdateTime = time.Now()
	return repositories.DownloadRepository.Update(sqls.DB(), t)
}

func (s *downloadService) Updates(id int64, columns map[string]interface{}) error {
	return repositories.DownloadRepository.Updates(sqls.DB(), id, columns)
}

func (s *downloadService) UpdateColumn(id int64, name string, value interface{}) error {
	return repositories.DownloadRepository.UpdateColumn(sqls.DB(), id, name, value)
}

func (s *downloadService) Delete(id int64) {
	repositories.DownloadRepository.Delete(sqls.DB(), id)
}

func (s *downloadService) GetActive() []models.Download {
	return repositories.DownloadRepository.Find(sqls.DB(), sqls.NewCnd().Where("status = ?", 0).Desc("id"))
}

func (s *downloadService) GetCategories() []string {
	var categories []string
	sqls.DB().Model(&models.Download{}).Where("status = ?", 0).Distinct().Pluck("category", &categories)
	return categories
}

func (s *downloadService) IncrDownloadCount(id int64) {
	sqls.DB().Exec("update t_download set download_count = download_count + 1 where id = ?", id)
}

func (s *downloadService) AddDownloadLog(userId, downloadId int64, ip, userAgent string) error {
	log := &models.DownloadLog{
		UserId:     userId,
		DownloadId: downloadId,
		Ip:         ip,
		UserAgent:  userAgent,
		CreateTime: time.Now(),
	}
	return repositories.DownloadLogRepository.Create(sqls.DB(), log)
}
