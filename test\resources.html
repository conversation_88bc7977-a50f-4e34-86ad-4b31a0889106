<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资料下载 - BBS-GO 技术论坛</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');
        body {
            font-family: 'Noto Sans SC', sans-serif;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
        <div class="container mx-auto px-4 flex items-center justify-between h-16">
            <!-- 左侧导航 -->
            <div class="flex items-center space-x-8">
                <a href="index.html" class="flex items-center space-x-2">
                    <img src="./images/logo.svg" alt="Logo" class="h-8 w-8">
                    <span class="font-bold text-xl hidden sm:block">BBS-GO</span>
                </a>
                <nav class="hidden md:flex space-x-6">
                    <a href="#" class="text-gray-500 hover:text-gray-900">话题</a>
                    <a href="articles.html" class="text-gray-500 hover:text-gray-900">文章</a>
                    <a href="resources.html" class="text-gray-900 font-medium">资料下载</a>
                    <a href="#" class="text-gray-500 hover:text-gray-900">BBS-GO</a>
                    <a href="#" class="text-gray-500 hover:text-gray-900">GitHub</a>
                    <a href="#" class="text-gray-500 hover:text-gray-900">Gitee</a>
                </nav>
            </div>

            <!-- 右侧功能区 -->
            <div class="flex items-center space-x-4">
                <!-- 搜索框 -->
                <div class="relative hidden md:block">
                    <input type="text" placeholder="搜索资料..." class="bg-gray-100 rounded-full py-2 px-4 pl-10 w-64 focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white">
                    <img src="./images/search.svg" alt="搜索" class="absolute left-3 top-2.5 h-5 w-5 text-gray-400">
                </div>

                <!-- 发表按钮 -->
                <button class="bg-blue-500 hover:bg-blue-600 text-white rounded-full px-4 py-2 flex items-center">
                    <img src="./images/plus.svg" alt="发表" class="h-5 w-5 mr-1 text-white">
                    <span>发表</span>
                </button>

                <!-- 消息通知 -->
                <a href="#" class="text-gray-500 hover:text-gray-900">
                    <img src="./images/message.svg" alt="消息" class="h-6 w-6">
                </a>

                <!-- 用户头像 -->
                <a href="#" class="flex items-center space-x-2">
                    <img src="https://via.placeholder.com/32" alt="用户头像" class="h-8 w-8 rounded-full">
                    <span class="hidden md:block">春光</span>
                </a>

                <!-- 暗色模式切换 -->
                <button class="text-gray-500 hover:text-gray-900">
                    <img src="./images/moon.svg" alt="暗色模式" class="h-6 w-6">
                </button>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="container mx-auto px-4 py-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold mb-2">技术资料下载中心</h1>
            <p class="text-gray-600">这里收集了各种高质量的技术资料，供社区成员学习和参考</p>
        </div>

        <!-- 资料分类标签 -->
        <div class="flex flex-wrap gap-2 mb-8">
            <button class="px-4 py-2 bg-blue-500 text-white rounded-full text-sm font-medium">全部</button>
            <button class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full text-sm font-medium">Go语言</button>
            <button class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full text-sm font-medium">前端开发</button>
            <button class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full text-sm font-medium">后端开发</button>
            <button class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full text-sm font-medium">数据库</button>
            <button class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full text-sm font-medium">DevOps</button>
            <button class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full text-sm font-medium">人工智能</button>
        </div>

        <!-- 资料列表 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Go语言资料 -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="p-6">
                    <div class="flex justify-between items-start">
                        <div>
                            <span class="inline-block px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs font-medium mb-2">Go语言</span>
                            <h2 class="text-xl font-bold mb-2">Go语言编程实战指南</h2>
                        </div>
                        <span class="text-xs text-gray-500">2.5MB</span>
                    </div>
                    <p class="text-gray-600 mb-4">从入门到精通的Go语言学习资料，包含基础语法、并发编程、网络编程等内容。</p>
                    <div class="flex justify-between items-center">
                        <div class="flex items-center text-sm text-gray-500">
                            <span>下载次数: 1,234</span>
                            <span class="mx-2">•</span>
                            <span>更新于: 2025-03-15</span>
                        </div>
                        <a href="#" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                            下载
                        </a>
                    </div>
                </div>
            </div>

            <!-- 前端开发资料 -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="p-6">
                    <div class="flex justify-between items-start">
                        <div>
                            <span class="inline-block px-2 py-1 bg-yellow-100 text-yellow-700 rounded text-xs font-medium mb-2">前端开发</span>
                            <h2 class="text-xl font-bold mb-2">现代JavaScript完全指南</h2>
                        </div>
                        <span class="text-xs text-gray-500">4.8MB</span>
                    </div>
                    <p class="text-gray-600 mb-4">涵盖ES6+、React、Vue等现代前端技术的全面指南，附带实战项目案例。</p>
                    <div class="flex justify-between items-center">
                        <div class="flex items-center text-sm text-gray-500">
                            <span>下载次数: 2,567</span>
                            <span class="mx-2">•</span>
                            <span>更新于: 2025-04-10</span>
                        </div>
                        <a href="#" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                            下载
                        </a>
                    </div>
                </div>
            </div>

            <!-- 后端开发资料 -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="p-6">
                    <div class="flex justify-between items-start">
                        <div>
                            <span class="inline-block px-2 py-1 bg-green-100 text-green-700 rounded text-xs font-medium mb-2">后端开发</span>
                            <h2 class="text-xl font-bold mb-2">微服务架构设计与实践</h2>
                        </div>
                        <span class="text-xs text-gray-500">3.7MB</span>
                    </div>
                    <p class="text-gray-600 mb-4">详解微服务架构的设计原则、实现方法和最佳实践，基于Go语言的实战案例。</p>
                    <div class="flex justify-between items-center">
                        <div class="flex items-center text-sm text-gray-500">
                            <span>下载次数: 1,876</span>
                            <span class="mx-2">•</span>
                            <span>更新于: 2025-02-28</span>
                        </div>
                        <a href="#" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                            下载
                        </a>
                    </div>
                </div>
            </div>

            <!-- 数据库资料 -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="p-6">
                    <div class="flex justify-between items-start">
                        <div>
                            <span class="inline-block px-2 py-1 bg-purple-100 text-purple-700 rounded text-xs font-medium mb-2">数据库</span>
                            <h2 class="text-xl font-bold mb-2">MySQL性能优化实战指南</h2>
                        </div>
                        <span class="text-xs text-gray-500">5.2MB</span>
                    </div>
                    <p class="text-gray-600 mb-4">MySQL数据库性能调优的实用技巧，包括索引优化、查询优化和服务器配置。</p>
                    <div class="flex justify-between items-center">
                        <div class="flex items-center text-sm text-gray-500">
                            <span>下载次数: 1,543</span>
                            <span class="mx-2">•</span>
                            <span>更新于: 2025-01-20</span>
                        </div>
                        <a href="#" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                            下载
                        </a>
                    </div>
                </div>
            </div>

            <!-- DevOps资料 -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="p-6">
                    <div class="flex justify-between items-start">
                        <div>
                            <span class="inline-block px-2 py-1 bg-red-100 text-red-700 rounded text-xs font-medium mb-2">DevOps</span>
                            <h2 class="text-xl font-bold mb-2">Docker与Kubernetes实战手册</h2>
                        </div>
                        <span class="text-xs text-gray-500">6.8MB</span>
                    </div>
                    <p class="text-gray-600 mb-4">容器化技术与编排工具的实战指南，从基础到高级应用，附带实战案例。</p>
                    <div class="flex justify-between items-center">
                        <div class="flex items-center text-sm text-gray-500">
                            <span>下载次数: 2,198</span>
                            <span class="mx-2">•</span>
                            <span>更新于: 2025-04-05</span>
                        </div>
                        <a href="#" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                            下载
                        </a>
                    </div>
                </div>
            </div>

            <!-- 人工智能资料 -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="p-6">
                    <div class="flex justify-between items-start">
                        <div>
                            <span class="inline-block px-2 py-1 bg-indigo-100 text-indigo-700 rounded text-xs font-medium mb-2">人工智能</span>
                            <h2 class="text-xl font-bold mb-2">机器学习与深度学习基础</h2>
                        </div>
                        <span class="text-xs text-gray-500">8.5MB</span>
                    </div>
                    <p class="text-gray-600 mb-4">机器学习与深度学习的基础理论与实践，包含常用算法与模型的详解。</p>
                    <div class="flex justify-between items-center">
                        <div class="flex items-center text-sm text-gray-500">
                            <span>下载次数: 1,987</span>
                            <span class="mx-2">•</span>
                            <span>更新于: 2025-03-22</span>
                        </div>
                        <a href="#" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                            下载
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <div class="mt-8 flex justify-center">
            <nav class="inline-flex rounded-md shadow">
                <a href="#" class="py-2 px-4 bg-white border border-gray-300 rounded-l-md text-gray-500 hover:bg-gray-50">上一页</a>
                <a href="#" class="py-2 px-4 bg-blue-500 border border-blue-500 text-white">1</a>
                <a href="#" class="py-2 px-4 bg-white border border-gray-300 text-gray-500 hover:bg-gray-50">2</a>
                <a href="#" class="py-2 px-4 bg-white border border-gray-300 text-gray-500 hover:bg-gray-50">3</a>
                <a href="#" class="py-2 px-4 bg-white border border-gray-300 rounded-r-md text-gray-500 hover:bg-gray-50">下一页</a>
            </nav>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t mt-8 py-8">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between">
                <div class="mb-6 md:mb-0">
                    <div class="flex items-center space-x-2">
                        <img src="./images/logo.svg" alt="Logo" class="h-8 w-8">
                        <span class="font-bold text-xl">BBS-GO</span>
                    </div>
                    <p class="text-gray-600 mt-2">基于Go语言开发的现代化社区系统</p>
                </div>
                
                <div class="grid grid-cols-2 md:grid-cols-3 gap-8">
                    <div>
                        <h3 class="font-medium mb-2">产品</h3>
                        <ul class="space-y-2 text-gray-600">
                            <li><a href="#" class="hover:text-gray-900">功能特性</a></li>
                            <li><a href="#" class="hover:text-gray-900">安装指南</a></li>
                            <li><a href="#" class="hover:text-gray-900">API文档</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="font-medium mb-2">资源</h3>
                        <ul class="space-y-2 text-gray-600">
                            <li><a href="#" class="hover:text-gray-900">开发者社区</a></li>
                            <li><a href="#" class="hover:text-gray-900">帮助中心</a></li>
                            <li><a href="#" class="hover:text-gray-900">贡献指南</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="font-medium mb-2">关于</h3>
                        <ul class="space-y-2 text-gray-600">
                            <li><a href="#" class="hover:text-gray-900">关于我们</a></li>
                            <li><a href="#" class="hover:text-gray-900">联系方式</a></li>
                            <li><a href="#" class="hover:text-gray-900">隐私政策</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="border-t mt-8 pt-6 text-center text-gray-600">
                <p>© 2025 BBS-GO. 保留所有权利。</p>
            </div>
        </div>
    </footer>

    <script>
        // 暗色模式切换
        function toggleDarkMode() {
            document.documentElement.classList.toggle('dark');
        }
    </script>
</body>
</html> 