<template>
  <section class="main">
    <div class="container main-container size-320">
      <div class="main-content no-padding no-bg topics-wrapper">
        <div class="topics-nav">
          <topics-nav />
        </div>
        <div class="topics-main">
          <!-- 使用新的Tab导航组件 -->
          <topics-tab-nav :current-tab="currentTab" />
          
          <load-more-async v-slot="{ results }" url="/api/topic/topics">
            <topic-list :topics="results" show-sticky />
          </load-more-async>
        </div>
      </div>
      
      <!-- 右侧边栏 -->
      <div class="right-container">
        <q-a-sidebar />
      </div>
    </div>
  </section>
</template>

<script setup>
// 当前页面是话题页，默认显示最新
const currentTab = ref('newest');

useHead({
  title: useSiteTitle("话题"),
});
</script>

<style lang="scss" scoped>
.main-container {
  &.size-320 {
    .right-container {
      min-width: 320px;
      max-width: 320px;
    }
    
    .main-content {
      width: calc(100% - 320px - 10px);
    }
  }
}

.right-container {
  padding-left: 10px;
}

// 响应式设计
@media screen and (max-width: 1024px) {
  .main-container.size-320 {
    .right-container {
      display: none !important;
    }
    
    .main-content {
      width: 100% !important;
    }
  }
}
</style>
