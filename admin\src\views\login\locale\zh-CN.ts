export default {
  'login.form.title': 'BBS-GO登录',
  'login.form.subtitle': '欢迎使用BBS-GO社区系统',
  'login.form.userName.errMsg': '用户名不能为空',
  'login.form.password.errMsg': '密码不能为空',
  'login.form.captchaCode.errMsg': '验证码不能为空',
  'login.form.login.errMsg': '登录出错，轻刷新重试',
  'login.form.login.success': '欢迎使用',
  'login.form.userName.placeholder': '用户名',
  'login.form.password.placeholder': '密码',
  'login.form.captchaCode.placeholder': '验证码',
  'login.form.forgetPassword': '忘记密码',
  'login.form.login': '登录',
  'login.banner.slogan1': '简洁至上',
  'login.banner.subSlogan1':
    'BBS-GO 的设计理念是简洁至上，注重去除冗余和不必要的复杂性，以提供清晰直观的用户界面和流畅的操作体验。',
  'login.banner.slogan2': '高度可定制',
  'login.banner.subSlogan2':
    '提供清晰的代码结构和模块化的设计，使得用户可以轻松进行二次开发和定制，以满足不同用户群体的需求。',
  'login.banner.slogan3': 'Go语言驱动',
  'login.banner.subSlogan3':
    '使用Go语言开发，充分发挥Go语言高效的并发模型、简单易读的语法以及快速的编译速度，从而提供高性能、稳定的系统。',
};
