<template>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-center mb-8">
      Tailwind CSS 测试页面
    </h1>
    
    <div class="max-w-2xl mx-auto">
      <!-- 基础工具类测试 -->
      <div class="bg-white shadow-lg rounded-lg p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">基础工具类测试</h2>
        <div class="space-y-4">
          <div class="p-4 bg-blue-100 text-blue-800 rounded">
            蓝色背景文本
          </div>
          <div class="p-4 bg-green-100 text-green-800 rounded">
            绿色背景文本
          </div>
          <div class="p-4 bg-red-100 text-red-800 rounded">
            红色背景文本
          </div>
        </div>
      </div>

      <!-- 响应式测试 -->
      <div class="bg-white shadow-lg rounded-lg p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">响应式布局测试</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div class="bg-gray-100 p-4 rounded text-center">
            <p>卡片 1</p>
          </div>
          <div class="bg-gray-100 p-4 rounded text-center">
            <p>卡片 2</p>
          </div>
          <div class="bg-gray-100 p-4 rounded text-center">
            <p>卡片 3</p>
          </div>
        </div>
      </div>

      <!-- 按钮测试 -->
      <div class="bg-white shadow-lg rounded-lg p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">按钮样式测试</h2>
        <div class="flex flex-wrap gap-4">
          <button class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
            蓝色按钮
          </button>
          <button class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
            绿色按钮
          </button>
          <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded hover:bg-gray-50">
            边框按钮
          </button>
        </div>
      </div>

      <!-- Tailwind 特色功能测试 -->
      <div class="bg-white shadow-lg rounded-lg p-6">
        <h2 class="text-xl font-semibold mb-4">Tailwind 特色功能</h2>
        <div class="space-y-4">
          <!-- 渐变背景 -->
          <div class="h-20 bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 rounded-lg flex items-center justify-center">
            <span class="text-white font-semibold">渐变背景</span>
          </div>
          
          <!-- 阴影效果 -->
          <div class="grid grid-cols-3 gap-4">
            <div class="p-4 bg-white shadow-sm rounded text-center">
              <p class="text-sm">轻微阴影</p>
            </div>
            <div class="p-4 bg-white shadow-md rounded text-center">
              <p class="text-sm">中等阴影</p>
            </div>
            <div class="p-4 bg-white shadow-lg rounded text-center">
              <p class="text-sm">较大阴影</p>
            </div>
          </div>
          
          <!-- 动画效果 -->
          <div class="flex gap-4">
            <div class="w-16 h-16 bg-blue-500 rounded-full animate-bounce"></div>
            <div class="w-16 h-16 bg-green-500 rounded-full animate-pulse"></div>
            <div class="w-16 h-16 bg-red-500 rounded-full animate-spin"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  title: 'Tailwind CSS 测试'
})
</script>

<style lang="scss" scoped>
/* 可以在这里添加额外的样式 */
</style> 