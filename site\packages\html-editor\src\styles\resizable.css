/**
 * 可调整大小图片的样式
 */

/* 阻止拖动时的默认行为 */
.ProseMirror img {
  -webkit-user-drag: none;
  user-select: none;
}

/* 防止拖动时文字被选中 */
.ProseMirror.resizing-image {
  user-select: none !important;
}

/* 拖动手柄样式 */
.resize-handle {
  position: absolute;
  width: 8px !important;
  height: 8px !important;
  background-color: #3b82f6 !important;
  border-radius: 50% !important;
  z-index: 100 !important;
  pointer-events: auto !important;
  touch-action: none !important;
  opacity: 1 !important;
  border: 1px solid white !important;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.3) !important;
}

/* 确保手柄位于正确位置 */
.resize-handle-nw {
  top: -4px !important;
  left: -4px !important;
  cursor: nwse-resize !important;
}

.resize-handle-n {
  top: -4px !important;
  left: calc(50% - 4px) !important;
  cursor: ns-resize !important;
}

.resize-handle-ne {
  top: -4px !important;
  right: -4px !important;
  cursor: nesw-resize !important;
}

.resize-handle-e {
  right: -4px !important;
  top: calc(50% - 4px) !important;
  cursor: ew-resize !important;
}

.resize-handle-se {
  bottom: -4px !important;
  right: -4px !important;
  cursor: nwse-resize !important;
}

.resize-handle-s {
  bottom: -4px !important;
  left: calc(50% - 4px) !important;
  cursor: ns-resize !important;
}

.resize-handle-sw {
  bottom: -4px !important;
  left: -4px !important;
  cursor: nesw-resize !important;
}

.resize-handle-w {
  left: -4px !important;
  top: calc(50% - 4px) !important;
  cursor: ew-resize !important;
}

/* 选中的图片边框 */
.resize-border {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 1px solid #3b82f6 !important;
  pointer-events: none;
  z-index: 90;
} 