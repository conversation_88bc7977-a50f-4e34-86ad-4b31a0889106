package repositories

import (
	"github.com/mlogclub/simple/sqls"
	"github.com/mlogclub/simple/web/params"
	"gorm.io/gorm"

	"bbs-go/internal/models"
)

var BannerRepository = newBannerRepository()

func newBannerRepository() *bannerRepository {
	return &bannerRepository{}
}

type bannerRepository struct {
}

func (r *bannerRepository) Get(db *gorm.DB, id int64) *models.Banner {
	ret := &models.Banner{}
	if err := db.First(ret, "id = ?", id).Error; err != nil {
		return nil
	}
	return ret
}

func (r *bannerRepository) Take(db *gorm.DB, where ...interface{}) *models.Banner {
	ret := &models.Banner{}
	if err := db.Take(ret, where...).Error; err != nil {
		return nil
	}
	return ret
}

func (r *bannerRepository) Find(db *gorm.DB, cnd *sqls.Cnd) (list []models.Banner) {
	cnd.Find(db, &list)
	return
}

func (r *bannerRepository) FindOne(db *gorm.DB, cnd *sqls.Cnd) *models.Banner {
	ret := &models.Banner{}
	if err := cnd.FindOne(db, &ret); err != nil {
		return nil
	}
	return ret
}

func (r *bannerRepository) FindPageByParams(db *gorm.DB, params *params.QueryParams) (list []models.Banner, paging *sqls.Paging) {
	return r.FindPageByCnd(db, &params.Cnd)
}

func (r *bannerRepository) FindPageByCnd(db *gorm.DB, cnd *sqls.Cnd) (list []models.Banner, paging *sqls.Paging) {
	cnd.Find(db, &list)
	count := cnd.Count(db, &models.Banner{})
	paging = &sqls.Paging{
		Page:  cnd.Paging.Page,
		Limit: cnd.Paging.Limit,
		Total: count,
	}
	return
}

func (r *bannerRepository) Create(db *gorm.DB, t *models.Banner) (err error) {
	err = db.Create(t).Error
	return
}

func (r *bannerRepository) Update(db *gorm.DB, t *models.Banner) (err error) {
	err = db.Save(t).Error
	return
}

func (r *bannerRepository) Updates(db *gorm.DB, id int64, columns map[string]interface{}) (err error) {
	err = db.Model(&models.Banner{}).Where("id = ?", id).Updates(columns).Error
	return
}

func (r *bannerRepository) UpdateColumn(db *gorm.DB, id int64, name string, value interface{}) (err error) {
	err = db.Model(&models.Banner{}).Where("id = ?", id).UpdateColumn(name, value).Error
	return
}

func (r *bannerRepository) Delete(db *gorm.DB, id int64) {
	db.Delete(&models.Banner{}, "id = ?", id)
}

// FindActive 查询启用的轮播图
func (r *bannerRepository) FindActive(db *gorm.DB) []models.Banner {
	return r.Find(db, sqls.NewCnd().Eq("status", 1).Desc("sort"))
}
