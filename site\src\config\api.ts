/**
 * API配置文件
 * 定义所有API端点和相关配置
 */

// API基础路径
export const API_BASE_URL = process.env.API_BASE_URL || '/api'

/**
 * 轮播图相关API端点
 */
export const BANNER_ENDPOINTS = {
  // 获取轮播图列表
  LIST: '/api/banner/list',
  // 获取推广图片列表  
  PROMOTIONS: '/api/banner/promotions',
  // 记录点击统计
  CLICK: '/api/banner/click',
  // 获取统计数据（管理后台用）
  STATS: '/api/banner/stats'
} as const

/**
 * 问答相关API端点
 */
export const QA_ENDPOINTS = {
  // 获取每日一问
  DAILY_QUESTION: '/api/topic/daily_question',
  // 获取热门回答列表
  HOT_ANSWERS: '/api/topic/hot_answers',
  // 获取精选问答列表
  FEATURED_QA: '/api/topic/featured_qa',
  // 获取七天问答采用榜
  WEEKLY_ADOPTION_RANK: '/api/topic/weekly_adoption_rank',
  // 获取问答统计数据（管理后台用）
  QA_STATS: '/api/qa/stats'
} as const

/**
 * 下载资料相关API端点
 */
export const DOWNLOAD_ENDPOINTS = {
  // 获取资料列表
  LIST: '/api/download/list',
  // 获取资料详情
  DETAIL: '/api/download',
  // 下载资料
  DOWNLOAD: '/api/download',
  // 获取资料分类
  CATEGORIES: '/api/download/categories',
} as const

/**
 * 轮播图默认配置
 */
export const BANNER_CONFIG = {
  // 自动播放间隔（毫秒）
  AUTOPLAY_INTERVAL: 5000,
  // 过渡动画时间（毫秒）
  TRANSITION_DURATION: 500,
  // 最大重试次数
  MAX_RETRY_COUNT: 3,
  // 请求超时时间（毫秒）
  REQUEST_TIMEOUT: 10000,
  // 是否启用错误上报
  ENABLE_ERROR_REPORTING: true,
  // 是否启用点击统计
  ENABLE_CLICK_TRACKING: true
} as const

/**
 * 下载资料默认配置
 */
export const DOWNLOAD_CONFIG = {
  // 最大重试次数
  MAX_RETRY_COUNT: 3,
  // 请求超时时间（毫秒）
  REQUEST_TIMEOUT: 15000,
} as const

/**
 * API响应状态码
 */
export const API_STATUS = {
  SUCCESS: 0,
  ERROR: 1,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500
} as const

/**
 * 错误信息配置
 */
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接异常，请检查网络设置',
  TIMEOUT_ERROR: '请求超时，请稍后重试',
  SERVER_ERROR: '服务器错误，请稍后重试',
  DATA_PARSE_ERROR: '数据解析错误',
  UNKNOWN_ERROR: '未知错误，请联系管理员'
} as const

/**
 * 缓存配置
 */
export const CACHE_CONFIG = {
  // 轮播图数据缓存时间（毫秒）
  BANNER_CACHE_TTL: 5 * 60 * 1000, // 5分钟
  // 推广图片数据缓存时间（毫秒）
  PROMOTION_CACHE_TTL: 10 * 60 * 1000, // 10分钟
  // 每日一问缓存时间（毫秒）
  DAILY_QUESTION_CACHE_TTL: 24 * 60 * 60 * 1000, // 24小时
  // 热门回答缓存时间（毫秒）
  HOT_ANSWERS_CACHE_TTL: 30 * 60 * 1000, // 30分钟
  // 精选问答缓存时间（毫秒）
  FEATURED_QA_CACHE_TTL: 60 * 60 * 1000, // 1小时
  // 问答采用榜缓存时间（毫秒）
  ADOPTION_RANK_CACHE_TTL: 2 * 60 * 60 * 1000, // 2小时
  // 下载资料缓存时间（毫秒）
  DOWNLOAD_CACHE_TTL: 15 * 60 * 1000, // 15分钟
  // 缓存键前缀
  CACHE_KEY_PREFIX: 'bbs_'
} as const 