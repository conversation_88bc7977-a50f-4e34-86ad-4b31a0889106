name: bbs-go-docker

on:
  push:
    branches:
      - "master"

jobs:
  buildx:
    runs-on: ubuntu-latest
    environment: docker
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      # - # Add support for more platforms with QEMU (optional)
      #   # https://github.com/docker/setup-qemu-action
      #   name: Set up QEMU
      #   uses: docker/setup-qemu-action@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Install docker-compose
        run: |
          sudo apt-get update
          sudo apt-get install -y docker-compose
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
      # - name: Build and push
      #   uses: docker/build-push-action@v5
      #   with:
      #     push: true
      #     tags: latest
      - name: Build and push docker image
        run: |
          docker-compose build
          docker-compose push
