package repositories

import (
	"bbs-go/internal/models"

	"github.com/mlogclub/simple/sqls"
	"github.com/mlogclub/simple/web/params"
	"gorm.io/gorm"
)

var DownloadRepository = newDownloadRepository()

func newDownloadRepository() *downloadRepository {
	return &downloadRepository{}
}

type downloadRepository struct {
}

func (r *downloadRepository) Get(db *gorm.DB, id int64) *models.Download {
	ret := &models.Download{}
	if err := db.First(ret, "id = ?", id).Error; err != nil {
		return nil
	}
	return ret
}

func (r *downloadRepository) Take(db *gorm.DB, where ...interface{}) *models.Download {
	ret := &models.Download{}
	if err := db.Take(ret, where...).Error; err != nil {
		return nil
	}
	return ret
}

func (r *downloadRepository) Find(db *gorm.DB, cnd *sqls.Cnd) (list []models.Download) {
	cnd.Find(db, &list)
	return
}

func (r *downloadRepository) FindOne(db *gorm.DB, cnd *sqls.Cnd) *models.Download {
	ret := &models.Download{}
	if err := cnd.FindOne(db, ret); err != nil {
		return nil
	}
	return ret
}

func (r *downloadRepository) FindPageByParams(db *gorm.DB, params *params.QueryParams) (list []models.Download, paging *sqls.Paging) {
	return r.FindPageByCnd(db, &params.Cnd)
}

func (r *downloadRepository) FindPageByCnd(db *gorm.DB, cnd *sqls.Cnd) (list []models.Download, paging *sqls.Paging) {
	cnd.Find(db, &list)
	count := cnd.Count(db, &models.Download{})
	paging = &sqls.Paging{
		Page:  cnd.Paging.Page,
		Limit: cnd.Paging.Limit,
		Total: count,
	}
	return
}

func (r *downloadRepository) Count(db *gorm.DB, cnd *sqls.Cnd) int64 {
	return cnd.Count(db, &models.Download{})
}

func (r *downloadRepository) Create(db *gorm.DB, t *models.Download) (err error) {
	err = db.Create(t).Error
	return
}

func (r *downloadRepository) Update(db *gorm.DB, t *models.Download) (err error) {
	err = db.Save(t).Error
	return
}

func (r *downloadRepository) Updates(db *gorm.DB, id int64, columns map[string]interface{}) (err error) {
	err = db.Model(&models.Download{}).Where("id = ?", id).Updates(columns).Error
	return
}

func (r *downloadRepository) UpdateColumn(db *gorm.DB, id int64, name string, value interface{}) (err error) {
	err = db.Model(&models.Download{}).Where("id = ?", id).UpdateColumn(name, value).Error
	return
}

func (r *downloadRepository) Delete(db *gorm.DB, id int64) {
	db.Delete(&models.Download{}, "id = ?", id)
}
