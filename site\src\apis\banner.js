/**
 * 轮播图和小图片相关API接口
 */
import { BANNER_ENDPOINTS, BANNER_CONFIG, API_STATUS, ERROR_MESSAGES } from '~/config/api'

/**
 * 获取首页轮播图列表
 * @returns {Promise} 轮播图数据
 */
export function getBannerList() {
  return useMyFetch(BANNER_ENDPOINTS.LIST, {
    method: 'GET',
    timeout: BANNER_CONFIG.REQUEST_TIMEOUT,
    retry: BANNER_CONFIG.MAX_RETRY_COUNT,
    default: () => [],
    server: true, // 确保在服务器端也执行
    onRequestError({ error }) {
      console.error('轮播图列表请求失败:', error)
      // 不抛出错误，返回默认值
      return [];
    },
    onResponseError({ response }) {
      console.error('轮播图列表响应错误:', response.status)
      // 不抛出错误，返回默认值
      return [];
    }
  });
}

/**
 * 获取首页小图片展示列表
 * @returns {Promise} 小图片数据
 */
export function getPromotionList() {
  return useMyFetch(BANNER_ENDPOINTS.PROMOTIONS, {
    method: 'GET',
    timeout: BANNER_CONFIG.REQUEST_TIMEOUT,
    retry: BANNER_CONFIG.MAX_RETRY_COUNT,
    default: () => [],
    server: true, // 确保在服务器端也执行
    onRequestError({ error }) {
      console.error('推广图片列表请求失败:', error)
      // 不抛出错误，返回默认值
      return [];
    },
    onResponseError({ response }) {
      console.error('推广图片列表响应错误:', response.status)
      // 不抛出错误，返回默认值
      return [];
    }
  });
}

/**
 * 记录轮播图点击统计
 * @param {number} bannerId 轮播图ID
 * @param {string} clickType 点击类型: 'banner' | 'promotion'
 * @returns {Promise} 统计结果
 */
export function recordBannerClick(bannerId, clickType = 'banner') {
  // 如果未启用点击统计，直接返回成功
  if (!BANNER_CONFIG.ENABLE_CLICK_TRACKING) {
    return Promise.resolve({
      success: true,
      message: 'click tracking disabled'
    });
  }

  return useMyFetch(BANNER_ENDPOINTS.CLICK, {
    method: 'POST',
    timeout: BANNER_CONFIG.REQUEST_TIMEOUT,
    body: {
      bannerId,
      clickType,
      timestamp: Date.now(),
      userAgent: process.client ? navigator.userAgent : '',
      referrer: process.client ? document.referrer : ''
    },
    default: () => true,
    server: false, // 点击统计只在客户端执行
    onRequestError({ error }) {
      console.warn('点击统计请求失败:', error)
      // 点击统计失败不影响用户体验，只记录警告
      return false;
    },
    onResponseError({ response }) {
      console.warn('点击统计响应错误:', response.status)
      return false;
    }
  });
}

/**
 * 获取轮播图统计数据（管理后台用）
 * @param {Object} params 查询参数
 * @param {string} params.startDate 开始日期
 * @param {string} params.endDate 结束日期
 * @param {string} params.type 统计类型
 * @returns {Promise} 统计数据
 */
export function getBannerStats(params = {}) {
  return useMyFetch(BANNER_ENDPOINTS.STATS, {
    method: 'GET',
    timeout: BANNER_CONFIG.REQUEST_TIMEOUT,
    query: params,
    default: () => ({
      totalClicks: 0,
      bannerStats: [],
      promotionStats: []
    }),
    onRequestError({ error }) {
      console.error('统计数据请求失败:', error)
      throw new Error(ERROR_MESSAGES.NETWORK_ERROR)
    },
    onResponseError({ response }) {
      console.error('统计数据响应错误:', response.status)
      throw new Error(ERROR_MESSAGES.SERVER_ERROR)
    }
  });
} 