<template>
  <div class="container">
    <Breadcrumb :items="['资料下载', '资料管理']" />

    <a-card class="general-card" title="资料列表">
      <a-row>
        <a-col :flex="1">
          <a-form :model="searchForm" label-align="left" class="search-form" @submit="handleSubmit">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="title" label="资料标题">
                  <a-input v-model="searchForm.title" placeholder="请输入资料标题" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="category" label="资料分类">
                  <a-select v-model="searchForm.category" placeholder="请选择资料分类" allow-clear>
                    <a-option value="">全部</a-option>
                    <a-option value="robot">机器人</a-option>
                    <a-option value="edge">边缘计算</a-option>
                    <a-option value="deep">深度学习</a-option>
                    <a-option value="ai">AI算法</a-option>
                    <a-option value="other">其他</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <template #icon>
                      <icon-search />
                    </template>
                    搜索
                  </a-button>
                  <a-button @click="resetSearch">
                    <template #icon>
                      <icon-refresh />
                    </template>
                    重置
                  </a-button>
                </a-space>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="openCreateModal">
              <template #icon>
                <icon-plus />
              </template>
              新增
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-table
        row-key="id"
        :loading="loading"
        :pagination="pagination"
        :data="renderData"
        :bordered="false"
        @page-change="onPageChange"
      >
        <template #columns>
          <a-table-column title="ID" data-index="id" :width="70" />
          <a-table-column title="标题" data-index="title" :ellipsis="true" />
          <a-table-column title="分类" data-index="category">
            <template #cell="{ record }">
              <a-tag>{{ getCategoryName(record.category) }}</a-tag>
            </template>
          </a-table-column>
          <a-table-column title="文件大小" data-index="fileSize">
            <template #cell="{ record }">
              {{ formatFileSize(record.fileSize) }}
            </template>
          </a-table-column>
          <a-table-column title="下载次数" data-index="downloadCount" />
          <a-table-column title="状态" data-index="status">
            <template #cell="{ record }">
              <a-tag :color="record.status === 0 ? 'green' : 'red'">
                {{ record.status === 0 ? '正常' : '禁用' }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column title="创建时间" data-index="createTime" />
          <a-table-column title="更新时间" data-index="updateTime" />
          <a-table-column title="操作" :width="180">
            <template #cell="{ record }">
              <a-space>
                <a-button type="text" size="small" @click="openEditModal(record)"> 编辑 </a-button>
                <a-button type="text" size="small" @click="previewFile(record)"> 预览 </a-button>
                <a-popconfirm content="确定要删除该资料吗？" @ok="handleDelete(record)">
                  <a-button type="text" status="danger" size="small"> 删除 </a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>

      <!-- 新增/编辑弹窗 -->
      <a-modal
        v-model:visible="modalVisible"
        :title="isEdit ? '编辑资料' : '新增资料'"
        @cancel="closeModal"
        @before-ok="handleSubmitForm"
      >
        <a-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-align="right"
          :label-col-props="{ span: 6 }"
          :wrapper-col-props="{ span: 18 }"
        >
          <a-form-item field="title" label="资料标题" required>
            <a-input v-model="form.title" placeholder="请输入资料标题" />
          </a-form-item>
          <a-form-item field="description" label="资料描述">
            <a-textarea
              v-model="form.description"
              placeholder="请输入资料描述"
              :auto-size="{ minRows: 3, maxRows: 5 }"
            />
          </a-form-item>
          <a-form-item field="category" label="资料分类" required>
            <a-select v-model="form.category" placeholder="请选择资料分类">
              <a-option value="robot">机器人</a-option>
              <a-option value="edge">边缘计算</a-option>
              <a-option value="deep">深度学习</a-option>
              <a-option value="ai">AI算法</a-option>
              <a-option value="other">其他</a-option>
            </a-select>
          </a-form-item>
          <a-form-item field="fileUrl" label="文件" required>
            <FileUpload
              v-model="form.fileUrl"
              :accept="'*'"
              upload-name="file"
              :show-manual-input="true"
              :max-size="100"
              @change="onFileChange"
            />
          </a-form-item>
          <a-form-item field="fileSize" label="文件大小(字节)">
            <a-input-number
              v-model="form.fileSize"
              placeholder="文件大小将自动填充"
              :min="0"
              :step="1024"
              :readonly="true"
              style="width: 100%"
            />
          </a-form-item>
          <a-form-item field="status" label="状态">
            <a-radio-group v-model="form.status">
              <a-radio :value="0">正常</a-radio>
              <a-radio :value="1">禁用</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-form>
      </a-modal>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import type { FormInstance } from '@arco-design/web-vue';
import {
  getDownloadList,
  createDownload,
  updateDownload,
  deleteDownload,
  type Download,
  type DownloadListParams,
  type DownloadListData,
  type DownloadFormData,
} from '@/api/download';
import FileUpload from '@/components/FileUpload.vue';

// 搜索表单
const searchForm = reactive<DownloadListParams>({
  title: '',
  category: '',
});

// 编辑表单
const form = reactive<Partial<Download>>({
  title: '',
  description: '',
  category: 'golang',
  fileUrl: '',
  fileSize: 0,
  status: 0,
});

// 表单校验规则
const rules = {
  title: [{ required: true, message: '请输入资料标题' }],
  category: [{ required: true, message: '请选择资料分类' }],
  fileUrl: [{ required: true, message: '请上传文件或输入文件URL' }],
};

// 状态变量
const loading = ref(false);
const renderData = ref<Download[]>([]);
const formRef = ref<FormInstance>();
const modalVisible = ref(false);
const isEdit = ref(false);

// 分页
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
});

// 获取资料列表
const fetchData = async () => {
  try {
    loading.value = true;
    const params: DownloadListParams = {
      page: pagination.current,
      limit: pagination.pageSize,
      ...searchForm,
    };

    // 尝试从API获取真实数据
    try {
      const response = await getDownloadList(params);
      console.log('response:', response);
      // 检查响应数据是否存在并且格式正确
      if (response && response.results !== undefined) {
        renderData.value = response.results || [];
        pagination.total = response.page ? response.page.total : 0;
      } else {
        // 如果响应数据格式不正确，设置为空数组
        renderData.value = [];
        pagination.total = 0;
        console.error('Invalid API response format:', response);
        Message.error('获取数据失败：响应格式不正确');
      }
    } catch (apiError) {
      renderData.value = [];
      pagination.total = 0;
      console.error(apiError);
      Message.error('获取资料列表失败');
    }
  } catch (error) {
    renderData.value = [];
    pagination.total = 0;
    console.error(error);
    Message.error('获取资料列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSubmit = () => {
  pagination.current = 1;
  fetchData();
};

// 重置搜索条件
const resetSearch = () => {
  searchForm.title = '';
  searchForm.category = '';
  pagination.current = 1;
  fetchData();
};

// 页码变化
const onPageChange = (current: number) => {
  pagination.current = current;
  fetchData();
};

// 打开创建弹窗
const openCreateModal = () => {
  isEdit.value = false;
  Object.assign(form, {
    title: '',
    description: '',
    category: 'golang',
    fileUrl: '',
    fileSize: 0,
    status: 0,
  });
  modalVisible.value = true;
};

// 打开编辑弹窗
const openEditModal = (record: Download) => {
  isEdit.value = true;
  Object.assign(form, record);
  modalVisible.value = true;
};

// 关闭弹窗
const closeModal = () => {
  modalVisible.value = false;
  formRef.value?.resetFields();
};

// 提交表单
const handleSubmitForm = async () => {
  const result = await formRef.value?.validate();
  if (!result) {
    try {
      if (isEdit.value && form.id) {
        await updateDownload(form.id, form as DownloadFormData);
        Message.success('更新成功');
      } else {
        await createDownload(form as DownloadFormData);
        Message.success('创建成功');
      }
      fetchData();
      return true;
    } catch (err) {
      Message.error(`${isEdit.value ? '更新' : '创建'}失败`);
      return false;
    }
  }
  return false;
};

// 删除资料
const handleDelete = async (record: Download) => {
  try {
    await deleteDownload(record.id);
    Message.success('删除成功');
    fetchData();
  } catch (err) {
    Message.error('删除失败');
  }
};

// 预览文件
const previewFile = (record: Download) => {
  window.open(record.fileUrl, '_blank');
};

// 文件上传变化处理
const onFileChange = (fileInfo: { url: string; name: string; size: number }) => {
  if (fileInfo.size > 0) {
    form.fileSize = fileInfo.size;
  }
};

// 获取分类名称
const getCategoryName = (category: string) => {
  const categoryMap: Record<string, string> = {
    golang: 'Go语言',
    frontend: '前端开发',
    backend: '后端开发',
    database: '数据库',
    devops: 'DevOps',
    ai: '人工智能',
  };
  return categoryMap[category] || category;
};

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
};

// 页面加载时获取数据
onMounted(() => {
  fetchData();
});
</script>

<style scoped>
.container {
  padding: 0 20px 20px;
}

:deep(.arco-table-th) {
  background-color: var(--color-fill-2);
}

.search-form {
  margin-bottom: 16px;
}
</style>
