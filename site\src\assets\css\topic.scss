.topics-wrapper {
  display: flex;
  width: 100%;

  .topics-nav {
    margin-right: 10px;
  }

  .topics-main {
    width: 100%;
    margin: 0 0 16px 0;
    // padding: 28px 0;
    background: var(--bg-color);
    border-radius: var(--border-radius);

    .topics-main-header {
      margin: 0 32px;
      padding: 24px 0;
      position: relative;
      font-style: normal;
      font-weight: 600;
      font-size: 20px;
      line-height: 16px;
      color: var(--text-color);
      border-bottom: 1px solid var(--border-color4);

      @media screen and (max-width: 768px) {
        & {
          margin: 0 12px;
        }
      }
    }
  }

  @media screen and (max-width: 768px) {
    .topics-nav {
      display: none;
    }
  }
}

.publish-form {
  border-radius: var(--border-radius);
  background: var(--bg-color);
  padding: 10px 18px 18px 18px;

  .form-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 10px;

    .form-title-name {
      font-size: 18px;
      font-weight: 500;
    }

    .form-title-switch {
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 10px;

      .iconfont {
        font-size: 18px;
        cursor: pointer;
      }

      .editor-type {
        display: flex;
        align-items: center;
        // gap: 5px;

        img {
          height: 18px;
          margin-top: -2px;
        }

        span {
          font-size: 14px;
          font-weight: 500;
          color: var(--text-color3);
        }
      }
    }
  }

  .form-footer {
    text-align: right;

    .btn-publish {
      width: 130px;
      color: #fff;
    }
  }

  .field {
    margin-bottom: 10px;

    input {
      border: 1px solid var(--border-color);
      background-color: var(--bg-color);
      border-radius: 3px;

      &:focus-visible {
        outline-width: 0;
      }

      &:focus {
        box-shadow: none;
      }
    }
  }

  .topic-tags {
    margin-bottom: 10px;
    display: flex;
    gap: 10px;

    .topic-tag {
      cursor: pointer;
      padding: 0 12px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 3px;
      background: var(--bg-color3);
      // border: 1px solid var(--border-color);
      color: var(--text-color3);
      font-size: 14px;
      line-height: 24px;

      &:hover {
        color: var(--text-link-color);
        background: var(--bg-color5);
        // border: 1px solid var(--border-hover-color);
      }

      &.selected {
        color: var(--text-color5);
        background: var(--text-link-color);
      }
    }
  }
}