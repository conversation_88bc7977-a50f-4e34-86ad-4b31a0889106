<template>
  <div class="qa-sidebar">
    <!-- 每日一问 -->
    <div v-if="dailyQuestion" class="widget">
      <div class="widget-header">
        <span class="widget-title">每日一问</span>
      </div>
      <div class="widget-content">
        <div class="daily-question">
          <h3 class="question-title">
            <nuxt-link :to="`/topic/${dailyQuestion.id}`">
              {{ dailyQuestion.title }}
            </nuxt-link>
          </h3>
          <div class="question-meta">
            <span class="author">{{ dailyQuestion.username || '匿名' }}</span>
            <span class="date">{{ dailyQuestion.date || useFormatDate(dailyQuestion.createTime, 'MM-dd') }}</span>
              </div>
          <div class="question-stats">
            <span class="answers">{{ dailyQuestion.answerCount }} 回答</span>
            <span class="views">{{ dailyQuestion.viewCount }} 浏览</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 热门回答 -->
    <div v-if="hotAnswers && hotAnswers.length > 0" class="widget">
      <div class="widget-header">
        <span class="widget-title">热门回答</span>
      </div>
      <div class="widget-content">
        <ul class="hot-answers">
          <li v-for="answer in hotAnswers" :key="answer.id" class="answer-item">
            <div class="answer-content">
              <nuxt-link :to="`/topic/${answer.id}`" class="answer-link">
                {{ answer.title }}
              </nuxt-link>
            </div>
            <div class="answer-meta">
              <span class="author">{{ answer.username || '匿名' }}</span>
              <span class="answers">{{ answer.answerCount }} 回答</span>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <!-- 精选问答 -->
    <div v-if="featuredQA && featuredQA.length > 0" class="widget">
      <div class="widget-header">
        <span class="widget-title">精选问答</span>
      </div>
      <div class="widget-content">
        <ul class="featured-qa">
          <li v-for="qa in featuredQA" :key="qa.id" class="qa-item">
            <div class="qa-content">
              <nuxt-link :to="`/topic/${qa.id}`" class="qa-link">
                {{ qa.title }}
              </nuxt-link>
            </div>
            <div class="qa-meta">
              <span class="author">{{ qa.username || '匿名' }}</span>
              <span class="answers">{{ qa.answerCount }} 回答</span>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <!-- 七天问答采用榜 -->
    <div v-if="weeklyRank && weeklyRank.length > 0" class="widget">
      <div class="widget-header">
        <span class="widget-title">七天问答采用榜</span>
      </div>
      <div class="widget-content">
        <ul class="weekly-rank">
          <li v-for="(user, index) in weeklyRank" :key="user.userId" class="rank-item">
            <span class="rank-number">{{ index + 1 }}</span>
            <div class="user-info">
              <div class="user-name-row">
                <span class="username">{{ user.username || '匿名' }}</span>
                <span class="adoption-count">{{ user.adoptionCount || user.totalLikes || 0 }}次采用</span>
               </div>
              <span class="stats">{{ user.answerCount }} 回答 • {{ user.totalLikes }} 赞</span>
             </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
// 获取每日一问
const { data: dailyQuestion } = await useMyFetch('/api/topic/daily_question');

// 获取热门回答
const { data: hotAnswers } = await useMyFetch('/api/topic/hot_answers', {
  params: { limit: 5 }
});

// 获取精选问答
const { data: featuredQA } = await useMyFetch('/api/topic/featured_qa', {
  params: { limit: 5 }
});

// 获取七天问答采用榜
const { data: weeklyRank } = await useMyFetch('/api/topic/weekly_adoption_rank', {
  params: { limit: 10 }
});
</script>

<style lang="scss" scoped>
.qa-sidebar {
  .widget {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 每日一问样式 - 与RightSidebar保持一致
.daily-question {
  .question-title {
    font-size: 15px;
    line-height: 1.5;
    color: var(--text-color);
    margin: 0 0 8px 0;
    font-weight: 500;
    
    a {
      color: var(--text-color);
      text-decoration: none;
      
      &:hover {
        color: var(--text-link-color);
      }
    }
  }
  
  .question-meta {
    margin-bottom: 8px;
    
    .author, .date {
      font-size: 13px;
      color: var(--text-color3);
      margin-right: 12px;
    }
  }
  
  .question-stats {
    .answers, .views {
      font-size: 13px;
      color: var(--text-color3);
      margin-right: 12px;
    }
  }
}

// 热门回答样式 - 与RightSidebar保持一致
.hot-answers {
  list-style: none;
  padding: 0;
  margin: 0;
  
  .answer-item {
    padding: 8px 0;
      border-bottom: 1px solid var(--border-color4);
      
      &:last-child {
        border-bottom: none;
      }
      
    .answer-content {
      margin-bottom: 4px;
      
      .answer-link {
          font-size: 14px;
          line-height: 1.4;
          color: var(--text-color);
          text-decoration: none;
          
          &:hover {
            color: var(--text-link-color);
          }
      }
        }
        
    .answer-meta {
      .author, .hot-score, .answers {
            font-size: 12px;
            color: var(--text-color3);
        margin-right: 12px;
      }
    }
  }
}

// 精选问答样式
.featured-qa {
  list-style: none;
  padding: 0;
  margin: 0;
  
  .qa-item {
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color4);
    
    &:last-child {
      border-bottom: none;
    }
    
    .qa-content {
      margin-bottom: 4px;
      
      .qa-link {
        font-size: 14px;
        line-height: 1.4;
        color: var(--text-color);
        text-decoration: none;
        
        &:hover {
          color: var(--text-link-color);
            }
      }
    }
    
    .qa-meta {
      .author, .answers {
        font-size: 12px;
        color: var(--text-color3);
        margin-right: 12px;
      }
    }
  }
}

// 七天问答采用榜样式
.weekly-rank {
  list-style: none;
  padding: 0;
  margin: 0;
  
  .rank-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
       border-bottom: 1px solid var(--border-color4);
       
       &:last-child {
         border-bottom: none;
       }
      
    .rank-number {
        width: 24px;
        height: 24px;
        min-width: 24px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 600;
        margin-right: 12px;
        background: var(--bg-color3);
        color: var(--text-color3);
      }
      
    .user-info {
         flex: 1;
         min-width: 0;
         
      .user-name-row {
           display: flex;
        justify-content: space-between;
           align-items: center;
        margin-bottom: 2px;
         
        .username {
           font-size: 14px;
           line-height: 1.4;
           color: var(--text-color);
           overflow: hidden;
           text-overflow: ellipsis;
           white-space: nowrap;
          flex: 1;
          min-width: 0;
         }
         
         .adoption-count {
          font-size: 12px;
          color: var(--text-link-color);
          font-weight: 500;
          margin-left: 8px;
          white-space: nowrap;
        }
      }
      
      .stats {
           font-size: 12px;
           color: var(--text-color3);
       }
    }
    
    // 前三名排名样式
    &:nth-child(1) .rank-number {
      background: #ff4757;
      color: white;
    }
    
    &:nth-child(2) .rank-number {
      background: #ff7675;
      color: white;
    }
    
    &:nth-child(3) .rank-number {
      background: #74b9ff;
      color: white;
    }
  }
}
</style> 