.container {
    /* padding: 10px; */
    margin: 10px;
    background-color: var(--color-bg-1);

    .container-header {
        display: flex;
        align-items: center;
        padding: 20px 10px 10px;
        background-color: var(--color-bg-1);
        border-bottom: 1px solid var(--color-border-1);

        .action-btns {
            display: flex;
            column-gap: 10px;
        }
    }

    .container-main {
        padding: 10px;
        overflow: auto;

         &::-webkit-scrollbar-track-piece {
            background: var(--color-neutral-3);
        }

        &::-webkit-scrollbar {
            width: 6px;
        }

        &::-webkit-scrollbar-thumb {
            background: var(--color-neutral-4);

            /* border-radius: 20px; */
        }

        /* background-color: var(--color-bg-1); */
    }
}