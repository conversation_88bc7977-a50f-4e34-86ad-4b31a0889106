<template>
  <div class="color-mode-btn" @click="switchColorMode">
    <svg
      v-if="$colorMode.preference === 'dark'"
      class="icon-day"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      data-v-6d1f5b88=""
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M13 1.1C13 0.768629 12.7761 0.5 12.5 0.5H11.5C11.2239 0.5 11 0.768629 11 1.1V2.9C11 3.23137 11.2239 3.5 11.5 3.5H12.5C12.7761 3.5 13 3.23137 13 2.9V1.1ZM20.4998 4.20711L19.7927 3.5C19.5975 3.30474 19.2809 3.30474 19.0856 3.5L17.6567 4.92894C17.4614 5.1242 17.4614 5.44078 17.6567 5.63605L18.3638 6.34315C18.5591 6.53841 18.8757 6.53841 19.0709 6.34315L20.4998 4.91421C20.6951 4.71895 20.6951 4.40237 20.4998 4.20711ZM12 4.5C16.1421 4.5 19.5 7.85786 19.5 12C19.5 16.1421 16.1421 19.5 12 19.5C7.85786 19.5 4.5 16.1421 4.5 12C4.5 7.85786 7.85786 4.5 12 4.5ZM5.63589 17.6569L6.343 18.364C6.53826 18.5592 6.53826 18.8758 6.343 19.0711L4.99995 20.4141C4.80469 20.6093 4.48811 20.6093 4.29284 20.4141L3.58574 19.707C3.39047 19.5117 3.39047 19.1951 3.58574 18.9999L4.92879 17.6569C5.12405 17.4616 5.44063 17.4616 5.63589 17.6569ZM12.5 20.5C12.7761 20.5 13 20.7239 13 21V23C13 23.2761 12.7761 23.5 12.5 23.5H11.5C11.2239 23.5 11 23.2761 11 23V21C11 20.7239 11.2239 20.5 11.5 20.5H12.5ZM20.4142 19.0002L19.0709 17.6569C18.8757 17.4616 18.5591 17.4616 18.3638 17.6569L17.6567 18.364C17.4614 18.5592 17.4614 18.8758 17.6567 19.0711L19 20.4144C19.1953 20.6096 19.5118 20.6096 19.7071 20.4144L20.4142 19.7073C20.6095 19.512 20.6095 19.1954 20.4142 19.0002ZM3.5 11.5C3.5 11.2239 3.27614 11 3 11H1C0.723858 11 0.5 11.2239 0.5 11.5V12.5C0.5 12.7761 0.723858 13 1 13H3C3.27614 13 3.5 12.7761 3.5 12.5V11.5ZM23 11C23.2761 11 23.5 11.2239 23.5 11.5V12.5C23.5 12.7761 23.2761 13 23 13H21C20.7239 13 20.5 12.7761 20.5 12.5V11.5C20.5 11.2239 20.7239 11 21 11H23ZM4.91414 3.50014L6.343 4.92894C6.53826 5.1242 6.53826 5.44079 6.343 5.63605L5.63589 6.34315C5.44063 6.53841 5.12405 6.53841 4.92879 6.34315L3.49992 4.91435C3.30466 4.71909 3.30466 4.40251 3.49992 4.20724L4.20703 3.50014C4.40229 3.30487 4.71887 3.30487 4.91414 3.50014Z"
        data-v-6d1f5b88=""
      ></path>
    </svg>
    <svg
      v-else
      class="icon-night"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      data-v-6d1f5b88=""
    >
      <path
        d="M13 2C13.6567 2 14.3059 2.06344 14.9409 2.18842L16.488 2.49289L15.553 3.76254C14.5494 5.12552 14 6.7699 14 8.5C14 12.3391 16.725 15.617 20.4453 16.3492L21.9921 16.6536L21.0575 17.9232C19.1853 20.4667 16.2196 22 13 22C7.47715 22 3 17.5228 3 12C3 6.47715 7.47715 2 13 2Z"
        data-v-6d1f5b88=""
      ></path>
    </svg>
  </div>
</template>

<script setup>
function switchColorMode() {
  const colorMode = useColorMode();
  if (colorMode.value === "dark") {
    colorMode.preference = "light";
  } else {
    colorMode.preference = "dark";
  }
}
</script>
<style lang="scss" scoped>
.color-mode-btn {
  position: relative;
  transition: all 0.15s linear;
  cursor: pointer;
  display: flex;
  align-items: center;

  &:hover {
    opacity: 0.8;
  }

  path {
    fill: var(--color-navbar-icon);
  }
}
</style>
