<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>震惊！！！bbs-go移动端凉了！！！ - BBS-GO 技术论坛</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');
        body {
            font-family: 'Noto Sans SC', sans-serif;
        }
        .comment-item:not(:last-child) {
            border-bottom: 1px solid #e5e7eb;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
        <div class="container mx-auto px-4 flex items-center justify-between h-16">
            <!-- 左侧导航 -->
            <div class="flex items-center space-x-8">
                <a href="index.html" class="flex items-center space-x-2">
                    <img src="./images/logo.svg" alt="Logo" class="h-8 w-8">
                    <span class="font-bold text-xl hidden sm:block">BBS-GO</span>
                </a>
                <nav class="hidden md:flex space-x-6">
                    <a href="#" class="text-gray-900 font-medium">话题</a>
                    <a href="articles.html" class="text-gray-500 hover:text-gray-900">文章</a>
                    <a href="resources.html" class="text-gray-500 hover:text-gray-900">资料下载</a>
                    <a href="#" class="text-gray-500 hover:text-gray-900">BBS-GO</a>
                    <a href="#" class="text-gray-500 hover:text-gray-900">GitHub</a>
                    <a href="#" class="text-gray-500 hover:text-gray-900">Gitee</a>
                </nav>
            </div>

            <!-- 右侧功能区 -->
            <div class="flex items-center space-x-4">
                <!-- 搜索框 -->
                <div class="relative hidden md:block">
                    <input type="text" placeholder="输入你想查找的内容" class="bg-gray-100 rounded-full py-2 px-4 pl-10 w-64 focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white">
                    <img src="./images/search.svg" alt="搜索" class="absolute left-3 top-2.5 h-5 w-5 text-gray-400">
                </div>

                <!-- 发表按钮 -->
                <button class="bg-blue-500 hover:bg-blue-600 text-white rounded-full px-4 py-2 flex items-center">
                    <img src="./images/plus.svg" alt="发表" class="h-5 w-5 mr-1 text-white">
                    <span>发表</span>
                </button>

                <!-- 消息通知 -->
                <a href="#" class="text-gray-500 hover:text-gray-900">
                    <img src="./images/message.svg" alt="消息" class="h-6 w-6">
                </a>

                <!-- 用户头像 -->
                <a href="#" class="flex items-center space-x-2">
                    <img src="https://via.placeholder.com/32" alt="用户头像" class="h-8 w-8 rounded-full">
                    <span class="hidden md:block">春光</span>
                </a>

                <!-- 暗色模式切换 -->
                <button class="text-gray-500 hover:text-gray-900">
                    <img src="./images/moon.svg" alt="暗色模式" class="h-6 w-6">
                </button>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="container mx-auto px-4 py-6 flex flex-col md:flex-row gap-6">
        <!-- 左侧内容区 -->
        <div class="flex-1">
            <!-- 帖子详情 -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                <div class="flex items-center space-x-3 mb-4">
                    <img src="https://via.placeholder.com/48" alt="qwer的头像" class="h-12 w-12 rounded-full">
                    <div>
                        <div class="flex items-center">
                            <h3 class="font-medium text-lg">qwer</h3>
                        </div>
                        <p class="text-gray-500 text-sm">发布于12天前</p>
                    </div>
                </div>
                
                <h1 class="text-2xl font-bold mb-4">震惊！！！bbs-go移动端凉了！！！</h1>
                
                <div class="text-gray-800 mb-6">
                    <p class="mb-4">什么情况? 我记得这个不是有移动端吗? 已经凉了??</p>
                </div>
                
                <div class="flex flex-wrap gap-2 mb-6">
                    <span class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">#交流</span>
                    <span class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">#移动端</span>
                    <span class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">#flutter</span>
                </div>
                
                <div class="flex items-center space-x-8 text-gray-500 border-t pt-4">
                    <div class="flex items-center space-x-2">
                        <img src="./images/thumbs-up.svg" alt="点赞" class="h-5 w-5">
                        <span>点赞 (2)</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <img src="./images/eye.svg" alt="浏览" class="h-5 w-5">
                        <span>浏览 (79)</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <img src="./images/bookmark.svg" alt="收藏" class="h-5 w-5">
                        <span>收藏</span>
                    </div>
                </div>
            </div>
            
            <!-- 评论区 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h2 class="text-lg font-medium mb-6">全部评论 2</h2>
                
                <!-- 评论输入框 -->
                <div class="mb-8">
                    <div class="border rounded-lg mb-4">
                        <textarea placeholder="请输入您想发表的内容..." class="w-full p-4 rounded-lg focus:outline-none" rows="4"></textarea>
                        <div class="flex justify-between items-center px-4 py-2 border-t bg-gray-50">
                            <button class="text-gray-500 hover:text-gray-700">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                    <circle cx="8.5" cy="8.5" r="1.5"></circle>
                                    <polyline points="21 15 16 10 5 21"></polyline>
                                </svg>
                            </button>
                            <div class="flex items-center">
                                <span class="text-xs text-gray-500 mr-2">Ctrl + Enter</span>
                                <button class="bg-green-500 text-white px-4 py-1 rounded">发表</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 评论列表 -->
                <div class="space-y-6">
                    <!-- 评论项 1 -->
                    <div class="comment-item pb-6">
                        <div class="flex">
                            <img src="https://via.placeholder.com/40" alt="城南花已开的头像" class="h-10 w-10 rounded-full mr-3">
                            <div class="flex-1">
                                <div class="flex justify-between">
                                    <div>
                                        <h3 class="font-medium">城南花已开</h3>
                                        <p class="text-gray-500 text-sm">11天前</p>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <p>简直太可惜啦</p>
                                </div>
                                <div class="mt-3 flex items-center space-x-4 text-gray-500">
                                    <button class="flex items-center space-x-1 hover:text-blue-500">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"></path>
                                        </svg>
                                        <span>点赞</span>
                                    </button>
                                    <button class="flex items-center space-x-1 hover:text-blue-500">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                                        </svg>
                                        <span>评论</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 评论项 2 -->
                    <div class="comment-item pb-6">
                        <div class="flex">
                            <img src="https://via.placeholder.com/40" alt="城南花已开的头像" class="h-10 w-10 rounded-full mr-3">
                            <div class="flex-1">
                                <div class="flex justify-between">
                                    <div>
                                        <h3 class="font-medium">城南花已开</h3>
                                        <p class="text-gray-500 text-sm">11天前</p>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <p>咕咕咕咕</p>
                                </div>
                                <div class="mt-3 flex items-center space-x-4 text-gray-500">
                                    <button class="flex items-center space-x-1 hover:text-blue-500">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"></path>
                                        </svg>
                                        <span>点赞 1</span>
                                    </button>
                                    <button class="flex items-center space-x-1 hover:text-blue-500">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                                        </svg>
                                        <span>评论</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 评论项 3 -->
                    <div class="comment-item pb-6">
                        <div class="flex">
                            <img src="https://via.placeholder.com/40" alt="城南花已开的头像" class="h-10 w-10 rounded-full mr-3">
                            <div class="flex-1">
                                <div class="flex justify-between">
                                    <div>
                                        <h3 class="font-medium">城南花已开</h3>
                                        <p class="text-gray-500 text-sm">11天前</p>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <p>111</p>
                                </div>
                                <div class="mt-3 flex items-center space-x-4 text-gray-500">
                                    <button class="flex items-center space-x-1 hover:text-blue-500">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"></path>
                                        </svg>
                                        <span>点赞</span>
                                    </button>
                                    <button class="flex items-center space-x-1 hover:text-blue-500">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                                        </svg>
                                        <span>评论</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 评论项 4 (带引用) -->
                    <div class="comment-item pb-6">
                        <div class="flex">
                            <img src="https://via.placeholder.com/40" alt="城南花已开的头像" class="h-10 w-10 rounded-full mr-3">
                            <div class="flex-1">
                                <div class="flex justify-between">
                                    <div>
                                        <h3 class="font-medium">城南花已开开发者城南花已开</h3>
                                        <p class="text-gray-500 text-sm">11天前</p>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <p>2222</p>
                                    <div class="mt-2 p-3 bg-gray-50 border-l-4 border-gray-200 text-gray-600">
                                        <p>111</p>
                                    </div>
                                </div>
                                <div class="mt-3 flex items-center space-x-4 text-gray-500">
                                    <button class="flex items-center space-x-1 hover:text-blue-500">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"></path>
                                        </svg>
                                        <span>点赞</span>
                                    </button>
                                    <button class="flex items-center space-x-1 hover:text-blue-500">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                                        </svg>
                                        <span>评论</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧侧边栏 -->
        <div class="w-full md:w-80 space-y-6">
            <!-- 作者信息卡片 -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="bg-blue-500 h-24 relative">
                    <img src="https://via.placeholder.com/80" alt="qwer的头像" class="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 h-20 w-20 rounded-full border-4 border-white">
                </div>
                <div class="pt-12 pb-6 px-6 text-center">
                    <h3 class="text-xl font-medium">qwer</h3>
                    <p class="text-gray-500 mt-1">这家伙很懒，什么都没写下</p>
                    
                    <div class="grid grid-cols-4 gap-4 mt-6 text-center">
                        <div>
                            <div class="font-medium">积分</div>
                            <div class="text-gray-500">10</div>
                        </div>
                        <div>
                            <div class="font-medium">话题</div>
                            <div class="text-gray-500">1</div>
                        </div>
                        <div>
                            <div class="font-medium">评论</div>
                            <div class="text-gray-500">0</div>
                        </div>
                        <div>
                            <div class="font-medium">注册排名</div>
                            <div class="text-gray-500">10244</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 相关话题 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-medium mb-4">相关话题</h3>
                <div class="space-y-4">
                    <a href="#" class="block hover:bg-gray-50 p-2 rounded">
                        <h4 class="font-medium">Flutter开发实践分享</h4>
                        <p class="text-gray-500 text-sm mt-1">分享Flutter开发移动应用的经验和技巧</p>
                    </a>
                    <a href="#" class="block hover:bg-gray-50 p-2 rounded">
                        <h4 class="font-medium">BBS-GO移动端开发计划</h4>
                        <p class="text-gray-500 text-sm mt-1">讨论BBS-GO移动端的未来发展方向</p>
                    </a>
                    <a href="#" class="block hover:bg-gray-50 p-2 rounded">
                        <h4 class="font-medium">移动端vs网页端：哪个更重要？</h4>
                        <p class="text-gray-500 text-sm mt-1">探讨现代社区系统的多平台策略</p>
                    </a>
                </div>
            </div>
            
            <!-- 热门标签 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-medium mb-4">热门标签</h3>
                <div class="flex flex-wrap gap-2">
                    <a href="#" class="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm">Flutter</a>
                    <a href="#" class="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm">移动开发</a>
                    <a href="#" class="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm">React Native</a>
                    <a href="#" class="px-3 py-1 bg-yellow-100 text-yellow-700 rounded-full text-sm">跨平台</a>
                    <a href="#" class="px-3 py-1 bg-red-100 text-red-700 rounded-full text-sm">BBS-GO</a>
                    <a href="#" class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">社区系统</a>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t mt-8 py-8">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between">
                <div class="mb-6 md:mb-0">
                    <div class="flex items-center space-x-2">
                        <img src="./images/logo.svg" alt="Logo" class="h-8 w-8">
                        <span class="font-bold text-xl">BBS-GO</span>
                    </div>
                    <p class="text-gray-600 mt-2">基于Go语言开发的现代化社区系统</p>
                </div>
                
                <div class="grid grid-cols-2 md:grid-cols-3 gap-8">
                    <div>
                        <h3 class="font-medium mb-2">产品</h3>
                        <ul class="space-y-2 text-gray-600">
                            <li><a href="#" class="hover:text-gray-900">功能特性</a></li>
                            <li><a href="#" class="hover:text-gray-900">安装指南</a></li>
                            <li><a href="#" class="hover:text-gray-900">API文档</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="font-medium mb-2">资源</h3>
                        <ul class="space-y-2 text-gray-600">
                            <li><a href="#" class="hover:text-gray-900">开发者社区</a></li>
                            <li><a href="#" class="hover:text-gray-900">帮助中心</a></li>
                            <li><a href="#" class="hover:text-gray-900">贡献指南</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="font-medium mb-2">关于</h3>
                        <ul class="space-y-2 text-gray-600">
                            <li><a href="#" class="hover:text-gray-900">关于我们</a></li>
                            <li><a href="#" class="hover:text-gray-900">联系方式</a></li>
                            <li><a href="#" class="hover:text-gray-900">隐私政策</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="border-t mt-8 pt-6 text-center text-gray-600">
                <p>© 2025 BBS-GO. 保留所有权利。</p>
            </div>
        </div>
    </footer>

    <script>
        // 暗色模式切换
        function toggleDarkMode() {
            document.documentElement.classList.toggle('dark');
        }
    </script>
</body>
</html> 