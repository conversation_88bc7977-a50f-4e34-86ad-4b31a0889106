@use "bulma/sass" with ( //
    // $widescreen-enabled: true,
    // $fullhd-enabled: true,
    // $body-background-color: var(--body-bg-color),

    $navbar-height: 4.15rem, // 修改导航栏高度为4rem (64px)
    $scheme-main: var(--scheme-main),
    $scheme-main-bis: var(--scheme-main),
    $scheme-main-ter: var(--scheme-main),

    $scheme-invert: var(--scheme-invert),
    $scheme-invert-bis: var(--scheme-invert),
    $scheme-invert-ter: var(--scheme-invert),
);

@mixin useCommon() {
    --border-radius: 12px;
    
    // 增大基础字体大小，提升阅读体验
    font-size: 15px; // 从默认的15px增加到16px
    line-height: 1.6;
}

@mixin useLightMode() {
    @include useCommon();
    --body-bg-color: #f3f3f3;
    --color-navbar-icon: #1878f3;

    --text: #333333;
    --navbar-box-shadow-color: whitesmoke;

    --text-color: #333333;
    --text-color2: #555;
    --text-color3: #70727c;
    --text-color3-hover: #70727c99;
    --text-color4: #d8d8d8;
    --text-color5: #fff;


    // --text-link-color: #3273dc;
    // --text-link-hover-color: #363636;
    --text-link-color: #1e80ff;
    --text-link-hover-color: #1171ee;

    --background: #fff;
    --bg-color: #fff;
    --bg-color2: rgba(247, 248, 250, 0.7);
    --bg-color3: #f2f3f5;
    --bg-color4: #f2f2f2;
    --bg-color5: #e8f3ff;
    --bg-color6: #f2f6ff;
    --bg-color6-hover: #f2f6ff99;
    --bg-color-alpha: rgba(29, 33, 41, 0.5);

    --border-color: #e9e9e9;
    --border-color2: #dcdfe6;
    --border-color3: #8590a6;
    --border-color4: #f5f5f5;
    --border-hover-color: #1878f3;

    --color-red: #fa5151;

    --button-text-color: #fff;
}

@mixin useDarkMode() {
    @include useCommon();

    --body-bg-color: #121212;
    --color-navbar-icon: #e3e3e3;

    --text: #fff;
    --navbar-box-shadow-color: #333;

    --text-color: findColorInvert(#4a4a4a);
    --text-color2: findColorInvert(#555);
    --text-color3: findColorInvert(#70727c);
    --text-color3-hover: #70727c99;
    --text-color4: findColorInvert(#d8d8d8);
    --text-color5: findColorInvert(#fff);

    // --text-link-color: #3273dc;
    // --text-link-hover-color: findColorInvert(#363636);
    --text-link-color: #1e80ff;
    --text-link-hover-color: #1171ee;

    --background: #272727;
    --bg-color: #272727;
    --bg-color2: #3a3a3a;
    --bg-color3: #3a3a3a;
    --bg-color4: #3a3a3a;
    --bg-color5: #3a3a3a;
    --bg-color6: #3a3a3a;
    --bg-color6-hover: #3a3a3a99;
    --bg-color-alpha: rgba(29, 33, 41, 0.5);

    --border-color: #4a4a4a;
    --border-color2: #4a4a4a;
    --border-color3: #4a4a4a;
    --border-color4: #4a4a4a;
    --border-hover-color: findColorInvert(#1878f3);

    --color-red: #fa5151;

    --button-text-color: #3a3a3a;

    .tag {
        background-color: #3a3a3a;

        &>a {
            color: var(--text);
        }
    }
}

// @media (prefers-color-scheme: light) {
//     :root {
//         @include useLightMode();
//     }
// }

// @media (prefers-color-scheme: dark) {
//     :root {
//         @include useDarkMode();
//     }
// }

[data-theme=light],
.theme-light {
    @include useLightMode();

    background: linear-gradient(136deg, #edf0ff99, #e7f6fa99);
}

[data-theme=dark],
.theme-dark {
    @include useDarkMode();

    background: linear-gradient(136deg, #181818, #121212);
}