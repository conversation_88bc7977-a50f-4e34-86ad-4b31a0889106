package uploader

import (
	"bbs-go/internal/models/dto"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"strings"

	"github.com/mlogclub/simple/common/strs"
)

// LocalUploader 本地文件上传器
type LocalUploader struct{}

// PutImage 上传图片
func (u *LocalUploader) PutImage(cfg dto.UploadConfig, data []byte, contentType string) (string, error) {
	if strs.IsBlank(contentType) {
		contentType = "image/jpeg"
	}
	key := generateImageKey(data, contentType)
	return u.PutObject(cfg, key, data, contentType)
}

// PutObject 上传文件对象
func (u *LocalUploader) PutObject(cfg dto.UploadConfig, key string, data []byte, contentType string) (string, error) {
	// 构建完整的文件路径
	fullPath := filepath.Join(cfg.Local.Path, key)

	// 确保目录存在
	dir := filepath.Dir(fullPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return "", fmt.Errorf("创建目录失败: %v", err)
	}

	// 写入文件
	if err := os.WriteFile(fullPath, data, 0644); err != nil {
		return "", fmt.Errorf("写入文件失败: %v", err)
	}

	// 构建访问URL
	url := u.buildURL(cfg, key)
	return url, nil
}

// CopyImage 复制远程图片到本地
func (u *LocalUploader) CopyImage(cfg dto.UploadConfig, originUrl string) (string, error) {
	data, contentType, err := download(originUrl)
	if err != nil {
		return "", err
	}
	return u.PutImage(cfg, data, contentType)
}

// buildURL 构建文件访问URL
func (u *LocalUploader) buildURL(cfg dto.UploadConfig, key string) string {
	host := cfg.Local.Host
	// 确保host以/结尾
	if !strings.HasSuffix(host, "/") {
		host += "/"
	}
	// 确保key不以/开头
	if strings.HasPrefix(key, "/") {
		key = key[1:]
	}
	// key已经包含了完整的路径结构，不需要再添加uploads/
	return host + "uploads/" + key
}

// ensureDir 确保目录存在
func ensureDir(path string) error {
	if _, err := os.Stat(path); os.IsNotExist(err) {
		return os.MkdirAll(path, fs.ModeDir|0755)
	}
	return nil
}
