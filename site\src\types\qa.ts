/**
 * 问答相关数据结构
 */

/**
 * 问题数据结构
 */
export interface QuestionItem {
  /** 问题ID */
  id: number;
  /** 问题标题 */
  title: string;
  /** 问题内容 */
  content?: string;
  /** 问题摘要 */
  summary?: string;
  /** 提问用户ID */
  userId: number;
  /** 提问用户名 */
  username: string;
  /** 用户头像 */
  avatar?: string;
  /** 回答数量 */
  answerCount: number;
  /** 点赞数量 */
  likeCount: number;
  /** 浏览数量 */
  viewCount: number;
  /** 是否精选 */
  isFeatured?: boolean;
  /** 是否已解决 */
  isSolved?: boolean;
  /** 问题状态：0-待回答，1-回答中，2-已解决 */
  status: 0 | 1 | 2;
  /** 标签列表 */
  tags?: string[];
  /** 创建时间 */
  createTime: string;
  /** 更新时间 */
  updateTime?: string;
  /** 最后回答时间 */
  lastAnswerTime?: string;
}

/**
 * 回答数据结构
 */
export interface AnswerItem {
  /** 回答ID */
  id: number;
  /** 问题ID */
  questionId: number;
  /** 回答内容 */
  content: string;
  /** 回答摘要 */
  summary?: string;
  /** 回答用户ID */
  userId: number;
  /** 回答用户名 */
  username: string;
  /** 用户头像 */
  avatar?: string;
  /** 点赞数量 */
  likeCount: number;
  /** 是否被采用 */
  isAdopted?: boolean;
  /** 是否精选回答 */
  isFeatured?: boolean;
  /** 创建时间 */
  createTime: string;
  /** 更新时间 */
  updateTime?: string;
}

/**
 * 每日一问数据结构
 */
export interface DailyQuestionItem extends QuestionItem {
  /** 日期标识 */
  date: string;
  /** 推荐理由 */
  reason?: string;
}

/**
 * 热门回答数据结构
 */
export interface HotAnswerItem extends QuestionItem {
  /** 热度分数 */
  hotScore: number;
  /** 最新回答摘要 */
  latestAnswerSummary?: string;
  /** 最新回答时间 */
  latestAnswerTime?: string;
}

/**
 * 精选问答数据结构
 */
export interface FeaturedQAItem extends QuestionItem {
  /** 精选时间 */
  featuredTime: string;
  /** 精选理由 */
  featuredReason?: string;
  /** 最佳回答摘要 */
  bestAnswerSummary?: string;
}

/**
 * 问答采用榜用户数据结构
 */
export interface AdoptionRankUser {
  /** 用户ID */
  userId: number;
  /** 用户名 */
  username: string;
  /** 用户头像 */
  avatar?: string;
  /** 被采用次数 */
  adoptedCount: number;
  /** 排名 */
  rank: number;
  /** 总回答数 */
  totalAnswers?: number;
  /** 采用率 */
  adoptionRate?: number;
  /** 统计周期内的数据 */
  periodData?: {
    /** 周期开始时间 */
    startDate: string;
    /** 周期结束时间 */
    endDate: string;
    /** 周期内被采用次数 */
    periodAdoptedCount: number;
    /** 周期内总回答数 */
    periodTotalAnswers: number;
  };
}

/**
 * 问答统计数据结构
 */
export interface QAStatsData {
  /** 总问题数 */
  totalQuestions: number;
  /** 总回答数 */
  totalAnswers: number;
  /** 精选问题数 */
  featuredCount: number;
  /** 已解决问题数 */
  solvedCount?: number;
  /** 活跃用户数 */
  activeUsers?: number;
  /** 每日统计 */
  dailyStats: Array<{
    /** 日期 */
    date: string;
    /** 新增问题数 */
    newQuestions: number;
    /** 新增回答数 */
    newAnswers: number;
    /** 解决问题数 */
    solvedQuestions?: number;
  }>;
}

/**
 * API响应数据结构
 */
export interface ApiResponse<T = any> {
  /** 响应代码，0表示成功 */
  code: number;
  /** 响应消息 */
  message: string;
  /** 响应数据 */
  data?: T;
}

/**
 * 每日一问响应
 */
export interface DailyQuestionResponse extends ApiResponse<DailyQuestionItem> {}

/**
 * 热门回答响应
 */
export interface HotAnswersResponse extends ApiResponse<HotAnswerItem[]> {}

/**
 * 精选问答响应
 */
export interface FeaturedQAResponse extends ApiResponse<FeaturedQAItem[]> {}

/**
 * 问答采用榜响应
 */
export interface AdoptionRankResponse extends ApiResponse<AdoptionRankUser[]> {}

/**
 * 问答统计响应
 */
export interface QAStatsResponse extends ApiResponse<QAStatsData> {}

/**
 * 问答配置选项
 */
export interface QAConfig {
  /** 每日一问缓存时间（毫秒） */
  dailyQuestionCacheTTL?: number;
  /** 热门回答缓存时间（毫秒） */
  hotAnswersCacheTTL?: number;
  /** 精选问答缓存时间（毫秒） */
  featuredQACacheTTL?: number;
  /** 采用榜缓存时间（毫秒） */
  adoptionRankCacheTTL?: number;
  /** 是否启用实时更新 */
  enableRealTimeUpdate?: boolean;
  /** 默认分页大小 */
  defaultPageSize?: number;
} 