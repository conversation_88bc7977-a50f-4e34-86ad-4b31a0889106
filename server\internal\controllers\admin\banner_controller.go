package admin

import (
	"time"

	"github.com/kataras/iris/v12"
	"github.com/mlogclub/simple/sqls"
	"github.com/mlogclub/simple/web"
	"github.com/mlogclub/simple/web/params"

	"bbs-go/internal/models"
	"bbs-go/internal/services"
)

type BannerController struct {
	Ctx iris.Context
}

// GetList 获取轮播图列表
func (c *BannerController) GetList() *web.JsonResult {
	list, paging := services.BannerService.FindPageByParams(params.NewQueryParams(c.Ctx).
		EqByReq("status").
		LikeByReq("title").
		PageByReq().Desc("id"))
	return web.JsonData(&web.PageResult{Results: list, Page: paging})
}

// GetBy 获取轮播图详情
func (c *BannerController) GetBy(id int64) *web.JsonResult {
	t := services.BannerService.Get(id)
	if t == nil {
		return web.JsonErrorMsg("数据不存在")
	}
	return web.JsonData(t)
}

// Post 创建轮播图
func (c *BannerController) Post() *web.JsonResult {
	t := &models.Banner{}
	err := params.ReadJSON(c.Ctx, t)
	if err != nil {
		return web.JsonErrorMsg(err.Error())
	}

	t.CreateTime = time.Now()
	t.UpdateTime = time.Now()
	err = services.BannerService.Create(t)
	if err != nil {
		return web.JsonErrorMsg(err.Error())
	}
	return web.JsonData(t)
}

// Put 更新轮播图
func (c *BannerController) Put() *web.JsonResult {
	id, err := params.FormValueInt64(c.Ctx, "id")
	if err != nil {
		return web.JsonErrorMsg(err.Error())
	}
	t := services.BannerService.Get(id)
	if t == nil {
		return web.JsonErrorMsg("数据不存在")
	}

	err = params.ReadForm(c.Ctx, t)
	if err != nil {
		return web.JsonErrorMsg(err.Error())
	}

	t.UpdateTime = time.Now()
	err = services.BannerService.Update(t)
	if err != nil {
		return web.JsonErrorMsg(err.Error())
	}
	return web.JsonData(t)
}

// Delete 删除轮播图
func (c *BannerController) Delete() *web.JsonResult {
	id, err := params.FormValueInt64(c.Ctx, "id")
	if err != nil {
		return web.JsonErrorMsg(err.Error())
	}

	services.BannerService.Delete(id)
	return web.JsonSuccess()
}

// GetStats 获取统计数据
func (c *BannerController) GetStats() *web.JsonResult {
	bannerStats := services.BannerService.Find(sqls.NewCnd().Desc("click_count").Limit(10))
	promotionStats := services.PromotionService.Find(sqls.NewCnd().Desc("click_count").Limit(10))

	var totalBannerClicks int64 = 0
	for _, banner := range bannerStats {
		totalBannerClicks += banner.ClickCount
	}

	var totalPromotionClicks int64 = 0
	for _, promotion := range promotionStats {
		totalPromotionClicks += promotion.ClickCount
	}

	return web.JsonData(map[string]interface{}{
		"totalClicks":    totalBannerClicks + totalPromotionClicks,
		"bannerStats":    bannerStats,
		"promotionStats": promotionStats,
	})
}
