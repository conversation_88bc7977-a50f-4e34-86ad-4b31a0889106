// @charset "UTF-8";

/* === 现有样式系统 === */
@use "./common";
@use "./topic";
@use "./highlightjs";
@use "./nav";
@use "./bulma.scss";

@import "./iconfont/iconfont.css";
@import '@asika32764/vue-animate/dist/vue-animate.css';

/* === 动画配置 === */
:root {
    --animate-duration: .3s;
    --animate-delay: 0;
}

/* === Tailwind CSS 兼容性 === */
/* Tailwind CSS 通过单独的 tailwind.css 文件引入 */
/* 两套样式系统可以共存，建议: */
/* 1. 新组件优先使用 Tailwind 工具类 */
/* 2. 现有组件保持 SCSS 样式不变 */
/* 3. 使用 tw- 前缀避免样式冲突 */