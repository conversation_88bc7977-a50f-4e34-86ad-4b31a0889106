<template>
  <div class="topics-tab-nav">
    <ul class="tab-list">
      <li :class="{ active: currentTab === 'recommend' }" class="tab-item">
        <nuxt-link to="/topics/node/recommend" class="tab-link">
          <span>推荐</span>
          <i class="tab-icon" :style="'background-image: url(' + iconRecommend + ')'"></i>
        </nuxt-link>
      </li>
      <li :class="{ active: currentTab === 'newest' }" class="tab-item">
        <nuxt-link to="/topics/node/newest" class="tab-link">
          <span>最新</span>
          <!-- <i class="tab-icon" :style="'background-image: url(' + iconNew + ')'"></i> -->
        </nuxt-link>
      </li>
      <li :class="{ active: currentTab === 'feed' }" class="tab-item">
        <nuxt-link to="/topics/node/feed" class="tab-link">
          <span>关注</span>
          <!-- <i class="tab-icon" :style="'background-image: url(' + iconFeed + ')'"></i> -->
        </nuxt-link>
      </li>
    </ul>
  </div>
</template>

<script setup>
import iconNew from "~/assets/images/new.png";
import iconRecommend from "~/assets/images/recommend2.png";
import iconFeed from "~/assets/images/feed.png";

const props = defineProps({
  currentTab: {
    type: String,
    default: 'newest'
  }
});
</script>

<style lang="scss" scoped>
.topics-tab-nav {
  background: var(--bg-color);
  border-bottom: 1px solid var(--border-color4);
  padding: 0 32px;
  
  .tab-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    
    .tab-item {
      margin-right: 32px;
      
      .tab-link {
        display: flex;
        align-items: center;
        padding: 16px 0;
        text-decoration: none;
        color: var(--text-color3);
        font-size: 15px;
        font-weight: 500;
        position: relative;
        transition: color 0.2s ease;
        
        .tab-icon {
          flex-shrink: 0;
          width: 20px;
          height: 20px;
          margin-right: 8px;
          background-position: center;
          background-repeat: no-repeat;
          background-size: 100% 100%;
          border-radius: 4px;
        }
        
        &:hover {
          color: var(--text-color);
        }
      }
      
      &.active .tab-link {
        color: var(--text-color);
        font-weight: 600;
        
        &:after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 3px;
          background: #e0245e;
          border-radius: 2px 2px 0 0;
        }
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .topics-tab-nav {
    padding: 0 12px;
    
    .tab-list .tab-item {
      margin-right: 20px;
      
      .tab-link {
        padding: 12px 0;
        font-size: 14px;
        
        .tab-icon {
          width: 18px;
          height: 18px;
          margin-right: 6px;
        }
      }
    }
  }
}
</style> 