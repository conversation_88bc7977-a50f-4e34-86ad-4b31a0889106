import axios from 'axios';

// API 端点常量
const DOWNLOAD_API = {
  LIST: '/api/admin/download/list',
  CREATE: '/api/admin/download/create',
  UPDATE: '/api/admin/download',
  DELETE: '/api/admin/download/delete',
  DETAIL: '/api/admin/download/:id',
  STATS: '/api/admin/download/stats',
};

/**
 * 下载资料相关接口类型定义
 */
export interface Download {
  id: number;
  title: string;
  description: string;
  category: string;
  fileUrl: string;
  fileSize: number;
  downloadCount: number;
  status: number;
  createTime: string;
  updateTime: string;
}

export interface DownloadListParams {
  page?: number;
  limit?: number;
  title?: string;
  category?: string;
}

export interface ApiResponse<T> {
  errorCode: number;
  message: string;
  data: T;
  success: boolean;
}

export interface DownloadListData {
  results: Download[];
  page: {
    page: number;
    limit: number;
    total: number;
  };
}

export interface DownloadStatsData {
  totalCount: number;
  totalDownloads: number;
  categoryStats: {
    category: string;
    name: string;
    count: number;
    downloads: number;
  }[];
}

export interface DownloadFormData {
  title: string;
  description: string;
  category: string;
  fileUrl: string;
  fileSize?: number;
  status?: number;
}

/**
 * 获取下载资料列表
 */
export function getDownloadList(params: any): Promise<DownloadListData> {
  return axios.get('/api/admin/download/list', { params });
}

/**
 * 获取下载资料详情
 */
export function getDownloadDetail(id: number): Promise<Download> {
  return axios.get(DOWNLOAD_API.DETAIL.replace(':id', id.toString()));
}

/**
 * 创建下载资料
 */
export function createDownload(data: DownloadFormData): Promise<Download> {
  return axios.post(DOWNLOAD_API.CREATE, data);
}

/**
 * 更新下载资料
 */
export function updateDownload(id: number, data: DownloadFormData): Promise<Download> {
  return axios.put(DOWNLOAD_API.UPDATE, { id, ...data });
}

/**
 * 删除下载资料
 */
export function deleteDownload(id: number): Promise<void> {
  return axios.post(DOWNLOAD_API.DELETE, { id });
}

/**
 * 获取下载统计
 */
export function getDownloadStats(): Promise<DownloadStatsData> {
  return axios.get(DOWNLOAD_API.STATS);
} 