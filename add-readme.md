# 新增预留接口说明文档

## 概述

本文档记录了为首页轮播图和推广图片功能新增的预留接口架构，包含所有相关文件、数据结构和API接口定义，方便后续后端对接和功能追溯。

**添加时间**: 2024年1月
**功能模块**: 首页轮播图和推广图片展示
**涉及页面**: 首页 (`site/src/pages/index.vue`)

---

## 新增文件清单

### 1. API接口层
- **`site/src/apis/banner.js`** - 轮播图相关API接口封装
- **`site/src/apis/qa.js`** - 问答相关API接口封装（新增）
- **`site/src/config/api.ts`** - API配置和常量定义

### 2. 类型定义层
- **`site/src/types/banner.ts`** - 轮播图和推广图片的TypeScript类型定义
- **`site/src/types/qa.ts`** - 问答相关的TypeScript类型定义（新增）

### 3. 组件层
- **`site/src/components/HomeBanner.vue`** - 首页轮播图组件（已更新集成API）
- **`site/src/components/RightSidebar.vue`** - 首页右侧边栏组件（已更新集成每日一问和热门回答API）
- **`site/src/components/QASidebar.vue`** - 问答页面右侧边栏组件（已更新集成精选问答和采用榜API）

### 4. 文档层
- **`site/src/docs/banner-api.md`** - 详细的API接口文档
- **`add-readme.md`** - 本文档，记录新增预留接口情况

---

## API接口定义

### 接口列表

#### 轮播图相关接口

| 序号 | 接口地址 | 请求方式 | 功能描述 | 状态 |
|------|----------|----------|----------|------|
| 1 | `/api/banner/list` | GET | 获取轮播图列表 | 📋 待实现 |
| 2 | `/api/banner/promotions` | GET | 获取推广图片列表 | 📋 待实现 |
| 3 | `/api/banner/click` | POST | 记录点击统计 | 📋 待实现 |
| 4 | `/api/banner/stats` | GET | 获取统计数据（管理后台） | 📋 待实现 |

#### 问答相关接口

| 序号 | 接口地址 | 请求方式 | 功能描述 | 状态 |
|------|----------|----------|----------|------|
| 5 | `/api/qa/daily-question` | GET | 获取每日一问 | 📋 待实现 |
| 6 | `/api/qa/hot-answers` | GET | 获取热门回答列表 | 📋 待实现 |
| 7 | `/api/qa/featured` | GET | 获取精选问答列表 | 📋 待实现 |
| 8 | `/api/qa/adoption-rank` | GET | 获取七天问答采用榜 | 📋 待实现 |
| 9 | `/api/qa/stats` | GET | 获取问答统计数据（管理后台） | 📋 待实现 |

### 1. 获取轮播图列表

```http
GET /api/banner/list
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 1,
      "title": "投稿得积分",
      "subtitle": "最高10000积分 超多积分礼品，等你来兑",
      "image": "https://example.com/banner1.jpg",
      "link": "/investment",
      "target": "_self",
      "sort": 100,
      "enabled": true,
      "startTime": "2024-01-01 00:00:00",
      "endTime": "2024-12-31 23:59:59",
      "createTime": "2024-01-01 00:00:00",
      "updateTime": "2024-01-01 00:00:00",
      "clickCount": 156
    }
  ]
}
```

### 2. 获取推广图片列表

```http
GET /api/banner/promotions
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 1,
      "title": "智能机器人开发套件",
      "subtitle": "",
      "image": "https://example.com/promotion1.jpg",
      "buttonText": "乐享机器人开发",
      "link": "/robot-dev",
      "target": "_self",
      "backgroundColor": "#6C5CE7",
      "textColor": "#FFFFFF",
      "sort": 100,
      "enabled": true,
      "startTime": "2024-01-01 00:00:00",
      "endTime": "2024-12-31 23:59:59",
      "createTime": "2024-01-01 00:00:00",
      "updateTime": "2024-01-01 00:00:00",
      "clickCount": 89
    }
  ]
}
```

### 3. 记录点击统计

```http
POST /api/banner/click
```

**请求参数**:
```json
{
  "bannerId": 1,
  "clickType": "banner",
  "timestamp": 1704067200000,
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
  "referrer": "https://example.com/previous-page"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success"
}
```

### 4. 获取统计数据

```http
GET /api/banner/stats?startDate=2024-01-01&endDate=2024-01-31&type=banner
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "totalClicks": 245,
    "bannerStats": [
      {
        "id": 1,
        "title": "投稿得积分",
        "clicks": 156,
        "date": "2024-01-01"
      }
    ],
    "promotionStats": [
      {
        "id": 1,
        "title": "智能机器人开发套件",
        "clicks": 89,
        "date": "2024-01-01"
      }
    ]
  }
}
```

### 5. 获取每日一问

```http
GET /api/qa/daily-question
```

**说明**: 随机显示一条当日的问题

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1001,
    "title": "如何在ROS2中实现多机器人协同SLAM？",
    "date": "2024-01-15",
    "userId": 1,
    "username": "机器人专家",
    "answerCount": 5,
    "likeCount": 12,
    "viewCount": 156,
    "status": 1,
    "reason": "该问题涉及多机器人协同的核心技术",
    "createTime": "2024-01-15T09:00:00Z"
  }
}
```

### 6. 获取热门回答列表

```http
GET /api/qa/hot-answers?limit=10
```

**说明**: 按回答次数从多到少显示问题，取前10条

**请求参数**:
- `limit`: 限制条数，默认10条

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 2001,
      "title": "ROS2节点通信机制详解",
      "hotScore": 1256,
      "userId": 1,
      "username": "通信专家",
      "answerCount": 25,
      "likeCount": 125,
      "viewCount": 1256,
      "status": 2,
      "latestAnswerSummary": "最新回答详细解释了DDS的工作原理...",
      "latestAnswerTime": "2024-01-15T14:30:00Z",
      "createTime": "2024-01-10T10:00:00Z"
    }
  ]
}
```

### 7. 获取精选问答列表

```http
GET /api/qa/featured?limit=5
```

**说明**: 查询精选字段为true的问题，展示5条

**请求参数**:
- `limit`: 限制条数，默认5条

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 3001,
      "title": "ROS2多机器人系统通信架构设计",
      "likeCount": 25,
      "answerCount": 8,
      "status": 1,
      "isFeatured": true,
      "featuredTime": "2024-01-15T10:00:00Z",
      "featuredReason": "该问题具有很高的技术价值",
      "bestAnswerSummary": "最佳回答提供了完整的架构设计方案...",
      "userId": 1,
      "username": "系统架构师",
      "viewCount": 580,
      "createTime": "2024-01-10T09:00:00Z"
    }
  ]
}
```

### 8. 获取七天问答采用榜

```http
GET /api/qa/adoption-rank?limit=10&days=7
```

**说明**: 按照问题回答的点赞次数从多到少展示，取前10条

**请求参数**:
- `limit`: 限制条数，默认10条
- `days`: 统计天数，默认7天

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "userId": 1,
      "username": "机器人大师",
      "avatar": "https://example.com/avatar1.jpg",
      "adoptedCount": 12,
      "rank": 1,
      "totalAnswers": 25,
      "adoptionRate": 48,
      "periodData": {
        "startDate": "2024-01-08",
        "endDate": "2024-01-15",
        "periodAdoptedCount": 12,
        "periodTotalAnswers": 20
      }
    }
  ]
}
```

### 9. 获取问答统计数据

```http
GET /api/qa/stats?startDate=2024-01-01&endDate=2024-01-31
```

**请求参数**:
- `startDate`: 开始日期（可选），格式：YYYY-MM-DD
- `endDate`: 结束日期（可选），格式：YYYY-MM-DD
- `type`: 统计类型（可选）

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "totalQuestions": 1250,
    "totalAnswers": 3680,
    "featuredCount": 45,
    "solvedCount": 856,
    "activeUsers": 125,
    "dailyStats": [
      {
        "date": "2024-01-15",
        "newQuestions": 15,
        "newAnswers": 42,
        "solvedQuestions": 8
      }
    ]
  }
}
```

---

## 数据结构定义

### 轮播图数据结构 (BannerItem)

```typescript
interface BannerItem {
  id: number;                    // 轮播图唯一ID
  title: string;                 // 轮播图标题
  subtitle?: string;             // 轮播图副标题
  image: string;                 // 图片URL地址
  link?: string;                 // 点击跳转链接
  target?: '_self' | '_blank';   // 链接打开方式
  sort?: number;                 // 排序权重，数字越大越靠前
  enabled?: boolean;             // 是否启用，默认true
  startTime?: string;            // 开始展示时间
  endTime?: string;              // 结束展示时间
  createTime?: string;           // 创建时间
  updateTime?: string;           // 更新时间
  clickCount?: number;           // 点击次数统计
}
```

### 推广图片数据结构 (PromotionItem)

```typescript
interface PromotionItem {
  id: number;                    // 推广图片唯一ID
  title: string;                 // 推广标题
  subtitle?: string;             // 推广副标题
  image: string;                 // 图片URL地址
  buttonText: string;            // 按钮文字
  link?: string;                 // 点击跳转链接
  target?: '_self' | '_blank';   // 链接打开方式
  backgroundColor?: string;      // 背景颜色（可选）
  textColor?: string;            // 文字颜色（可选）
  sort?: number;                 // 排序权重
  enabled?: boolean;             // 是否启用
  startTime?: string;            // 开始展示时间
  endTime?: string;              // 结束展示时间
  createTime?: string;           // 创建时间
  updateTime?: string;           // 更新时间
  clickCount?: number;           // 点击次数统计
}
```

### 问题基础数据结构 (QuestionItem)

```typescript
interface QuestionItem {
  id: number;                    // 问题ID
  title: string;                 // 问题标题
  content?: string;              // 问题内容
  summary?: string;              // 问题摘要
  userId: number;                // 提问用户ID
  username: string;              // 提问用户名
  avatar?: string;               // 用户头像
  answerCount: number;           // 回答数量
  likeCount: number;             // 点赞数量
  viewCount: number;             // 浏览数量
  isFeatured?: boolean;          // 是否精选
  isSolved?: boolean;            // 是否已解决
  status: 0 | 1 | 2;            // 问题状态：0-待回答，1-回答中，2-已解决
  tags?: string[];               // 标签列表
  createTime: string;            // 创建时间
  updateTime?: string;           // 更新时间
  lastAnswerTime?: string;       // 最后回答时间
}
```

### 每日一问数据结构 (DailyQuestionItem)

```typescript
interface DailyQuestionItem extends QuestionItem {
  date: string;                  // 日期标识
  reason?: string;               // 推荐理由
}
```

### 热门回答数据结构 (HotAnswerItem)

```typescript
interface HotAnswerItem extends QuestionItem {
  hotScore: number;              // 热度分数
  latestAnswerSummary?: string;  // 最新回答摘要
  latestAnswerTime?: string;     // 最新回答时间
}
```

### 精选问答数据结构 (FeaturedQAItem)

```typescript
interface FeaturedQAItem extends QuestionItem {
  featuredTime: string;          // 精选时间
  featuredReason?: string;       // 精选理由
  bestAnswerSummary?: string;    // 最佳回答摘要
}
```

### 问答采用榜用户数据结构 (AdoptionRankUser)

```typescript
interface AdoptionRankUser {
  userId: number;                // 用户ID
  username: string;              // 用户名
  avatar?: string;               // 用户头像
  adoptedCount: number;          // 被采用次数
  rank: number;                  // 排名
  totalAnswers?: number;         // 总回答数
  adoptionRate?: number;         // 采用率
  periodData?: {                 // 统计周期内的数据
    startDate: string;           // 周期开始时间
    endDate: string;             // 周期结束时间
    periodAdoptedCount: number;  // 周期内被采用次数
    periodTotalAnswers: number;  // 周期内总回答数
  };
}
```

### API响应结构

```typescript
interface ApiResponse<T = any> {
  code: number;                  // 响应代码，0表示成功
  message: string;               // 响应消息
  data?: T;                      // 响应数据
}
```

---

## 配置参数

### API配置 (`site/src/config/api.ts`)

```typescript
// API端点配置
export const BANNER_ENDPOINTS = {
  LIST: '/api/banner/list',           // 获取轮播图列表
  PROMOTIONS: '/api/banner/promotions', // 获取推广图片列表
  CLICK: '/api/banner/click',         // 记录点击统计
  STATS: '/api/banner/stats'          // 获取统计数据
}

// 问答相关API端点
export const QA_ENDPOINTS = {
  DAILY_QUESTION: '/api/qa/daily-question',     // 获取每日一问
  HOT_ANSWERS: '/api/qa/hot-answers',           // 获取热门回答列表
  FEATURED_QA: '/api/qa/featured',              // 获取精选问答列表
  WEEKLY_ADOPTION_RANK: '/api/qa/adoption-rank', // 获取七天问答采用榜
  QA_STATS: '/api/qa/stats'                     // 获取问答统计数据
}

// 功能配置
export const BANNER_CONFIG = {
  AUTOPLAY_INTERVAL: 5000,        // 自动播放间隔（毫秒）
  TRANSITION_DURATION: 500,       // 过渡动画时间（毫秒）
  MAX_RETRY_COUNT: 3,             // 最大重试次数
  REQUEST_TIMEOUT: 10000,         // 请求超时时间（毫秒）
  ENABLE_ERROR_REPORTING: true,   // 是否启用错误上报
  ENABLE_CLICK_TRACKING: true     // 是否启用点击统计
}

// 响应状态码
export const API_STATUS = {
  SUCCESS: 0,                     // 成功
  ERROR: 1,                       // 一般错误
  UNAUTHORIZED: 401,              // 未授权
  FORBIDDEN: 403,                 // 禁止访问
  NOT_FOUND: 404,                 // 资源不存在
  SERVER_ERROR: 500               // 服务器内部错误
}
```

---

## 前端实现特性

### 1. 错误处理机制
- ✅ **自动重试**: 网络请求失败时自动重试，最多3次
- ✅ **超时处理**: 默认10秒超时，可配置
- ✅ **降级策略**: API失败时使用默认数据保证页面正常显示
- ✅ **用户友好**: 错误状态显示重试按钮

### 2. 性能优化
- ✅ **并行加载**: 轮播图和推广图片数据并行请求
- ✅ **缓存策略**: 轮播图缓存5分钟，推广图片缓存10分钟，每日一问缓存24小时，热门回答缓存30分钟，精选问答缓存1小时，问答采用榜缓存2小时
- ✅ **骨架屏**: 加载时显示骨架屏动画
- ✅ **懒加载**: 组件挂载后才开始数据加载

### 3. 交互增强
- ✅ **点击统计**: 记录用户点击行为，支持关闭
- ✅ **链接处理**: 支持当前窗口和新窗口打开
- ✅ **自动播放**: 可配置的自动轮播功能
- ✅ **响应式**: 完美适配桌面和移动端

### 4. 开发体验
- ✅ **TypeScript**: 完整的类型定义
- ✅ **配置化**: 所有参数可配置
- ✅ **文档完善**: 详细的API文档和注释
- ✅ **错误日志**: 完整的错误信息记录

---

## 后端对接清单

### 必须实现的接口

1. **轮播图列表接口** ⭐⭐⭐ 
   - 接口: `GET /api/banner/list`
   - 优先级: 高
   - 说明: 首页轮播图核心数据

2. **推广图片列表接口** ⭐⭐⭐
   - 接口: `GET /api/banner/promotions` 
   - 优先级: 高
   - 说明: 首页推广图片核心数据

3. **点击统计接口** ⭐⭐
   - 接口: `POST /api/banner/click`
   - 优先级: 中
   - 说明: 用户行为统计，可后期实现

4. **统计数据接口** ⭐
   - 接口: `GET /api/banner/stats`
   - 优先级: 低
   - 说明: 管理后台统计功能

5. **每日一问接口** ⭐⭐⭐
   - 接口: `GET /api/qa/daily-question`
   - 优先级: 高
   - 说明: 随机显示一条当日的问题

6. **热门回答接口** ⭐⭐⭐
   - 接口: `GET /api/qa/hot-answers`
   - 优先级: 高
   - 说明: 按回答次数从多到少显示问题，取前10条

7. **精选问答接口** ⭐⭐⭐
   - 接口: `GET /api/qa/featured`
   - 优先级: 高
   - 说明: 查询精选字段为true的问题，展示5条

8. **问答采用榜接口** ⭐⭐
   - 接口: `GET /api/qa/adoption-rank`
   - 优先级: 中
   - 说明: 按照问题回答的点赞次数从多到少展示，取前10条

9. **问答统计接口** ⭐
   - 接口: `GET /api/qa/stats`
   - 优先级: 低
   - 说明: 管理后台问答统计功能

### 建议的数据库表结构

#### 轮播图表 (banners)
```sql
CREATE TABLE banners (
  id INT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(255) NOT NULL COMMENT '标题',
  subtitle VARCHAR(500) COMMENT '副标题',
  image VARCHAR(500) NOT NULL COMMENT '图片URL',
  link VARCHAR(500) COMMENT '跳转链接',
  target ENUM('_self', '_blank') DEFAULT '_self' COMMENT '打开方式',
  sort INT DEFAULT 0 COMMENT '排序权重',
  enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  start_time DATETIME COMMENT '开始时间',
  end_time DATETIME COMMENT '结束时间',
  click_count INT DEFAULT 0 COMMENT '点击次数',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_enabled_sort (enabled, sort),
  INDEX idx_time_range (start_time, end_time)
);
```

#### 推广图片表 (promotions)
```sql
CREATE TABLE promotions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(255) NOT NULL COMMENT '标题',
  subtitle VARCHAR(500) COMMENT '副标题',
  image VARCHAR(500) NOT NULL COMMENT '图片URL',
  button_text VARCHAR(100) NOT NULL COMMENT '按钮文字',
  link VARCHAR(500) COMMENT '跳转链接',
  target ENUM('_self', '_blank') DEFAULT '_self' COMMENT '打开方式',
  background_color VARCHAR(20) COMMENT '背景颜色',
  text_color VARCHAR(20) COMMENT '文字颜色',
  sort INT DEFAULT 0 COMMENT '排序权重',
  enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  start_time DATETIME COMMENT '开始时间',
  end_time DATETIME COMMENT '结束时间',
  click_count INT DEFAULT 0 COMMENT '点击次数',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_enabled_sort (enabled, sort),
  INDEX idx_time_range (start_time, end_time)
);
```

#### 点击统计表 (banner_clicks)
```sql
CREATE TABLE banner_clicks (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  banner_id INT NOT NULL COMMENT '轮播图/推广图片ID',
  click_type ENUM('banner', 'promotion') NOT NULL COMMENT '点击类型',
  user_agent TEXT COMMENT '用户代理',
  referrer VARCHAR(500) COMMENT '来源页面',
  ip_address VARCHAR(45) COMMENT 'IP地址',
  click_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '点击时间',
  INDEX idx_banner_type (banner_id, click_type),
  INDEX idx_click_time (click_time)
);
```

#### 问题表 (questions)
```sql
CREATE TABLE questions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(500) NOT NULL COMMENT '问题标题',
  content TEXT COMMENT '问题内容',
  summary VARCHAR(1000) COMMENT '问题摘要',
  user_id INT NOT NULL COMMENT '提问用户ID',
  answer_count INT DEFAULT 0 COMMENT '回答数量',
  like_count INT DEFAULT 0 COMMENT '点赞数量',
  view_count INT DEFAULT 0 COMMENT '浏览数量',
  is_featured BOOLEAN DEFAULT FALSE COMMENT '是否精选',
  is_solved BOOLEAN DEFAULT FALSE COMMENT '是否已解决',
  status TINYINT DEFAULT 0 COMMENT '问题状态：0-待回答，1-回答中，2-已解决',
  featured_time DATETIME COMMENT '精选时间',
  featured_reason VARCHAR(500) COMMENT '精选理由',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  last_answer_time DATETIME COMMENT '最后回答时间',
  INDEX idx_user_id (user_id),
  INDEX idx_status (status),
  INDEX idx_featured (is_featured, featured_time),
  INDEX idx_hot_score (answer_count, like_count, view_count),
  INDEX idx_create_time (create_time),
  FULLTEXT idx_title_content (title, content)
);
```

#### 回答表 (answers)
```sql
CREATE TABLE answers (
  id INT PRIMARY KEY AUTO_INCREMENT,
  question_id INT NOT NULL COMMENT '问题ID',
  content TEXT NOT NULL COMMENT '回答内容',
  summary VARCHAR(1000) COMMENT '回答摘要',
  user_id INT NOT NULL COMMENT '回答用户ID',
  like_count INT DEFAULT 0 COMMENT '点赞数量',
  is_adopted BOOLEAN DEFAULT FALSE COMMENT '是否被采用',
  is_featured BOOLEAN DEFAULT FALSE COMMENT '是否精选回答',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_question_id (question_id),
  INDEX idx_user_id (user_id),
  INDEX idx_adopted (is_adopted, create_time),
  INDEX idx_like_count (like_count),
  FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE
);
```

#### 问题标签表 (question_tags)
```sql
CREATE TABLE question_tags (
  id INT PRIMARY KEY AUTO_INCREMENT,
  question_id INT NOT NULL COMMENT '问题ID',
  tag_name VARCHAR(50) NOT NULL COMMENT '标签名称',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_question_id (question_id),
  INDEX idx_tag_name (tag_name),
  UNIQUE KEY uk_question_tag (question_id, tag_name),
  FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE
);
```

#### 每日一问记录表 (daily_questions)
```sql
CREATE TABLE daily_questions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  question_id INT NOT NULL COMMENT '问题ID',
  date DATE NOT NULL COMMENT '日期',
  reason VARCHAR(500) COMMENT '推荐理由',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_date (date),
  INDEX idx_question_id (question_id),
  FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE
);
```

---

## 测试建议

### 1. 接口测试
- 使用 Postman 或类似工具测试所有API接口
- 验证响应数据格式是否符合定义
- 测试异常情况下的错误响应

### 2. 功能测试
- 轮播图自动播放和手动切换
- 推广图片点击跳转
- 加载状态和错误处理
- 移动端响应式适配

### 3. 性能测试
- 图片加载速度
- API响应时间
- 缓存机制验证
- 并发请求处理

---

## 注意事项

### 1. 安全考虑
- 图片URL验证，防止XSS攻击
- 跳转链接白名单机制
- API访问频率限制
- 用户隐私保护（点击统计）

### 2. 运维考虑
- 图片CDN配置和缓存
- 数据库查询优化
- 监控和日志记录
- 备份和恢复策略

### 3. 扩展性考虑
- 支持多语言国际化
- 支持A/B测试功能
- 支持定时任务管理
- 支持批量操作接口

---

## 问答功能查询规则

### 1. 每日一问
- **查询逻辑**: 随机显示一条当日的问题
- **实现方式**: 
  ```sql
  -- 优先从daily_questions表获取当日推荐问题
  SELECT q.* FROM questions q 
  JOIN daily_questions dq ON q.id = dq.question_id 
  WHERE dq.date = CURDATE()
  
  -- 如果当日没有推荐问题，则随机选择一个热门问题
  SELECT * FROM questions 
  WHERE status IN (0, 1) AND answer_count > 0
  ORDER BY RAND() LIMIT 1
  ```

### 2. 热门回答
- **查询逻辑**: 按回答次数从多到少显示问题，取前10条
- **排序规则**: 优先按answer_count降序，再按like_count降序
- **实现方式**:
  ```sql
  SELECT * FROM questions 
  WHERE status IN (0, 1, 2)
  ORDER BY answer_count DESC, like_count DESC, view_count DESC
  LIMIT 10
  ```

### 3. 精选问答
- **查询逻辑**: 查询is_featured=true的问题，展示5条
- **排序规则**: 按精选时间(featured_time)降序
- **实现方式**:
  ```sql
  SELECT * FROM questions 
  WHERE is_featured = TRUE
  ORDER BY featured_time DESC
  LIMIT 5
  ```

### 4. 七天问答采用榜
- **查询逻辑**: 按照问题回答的点赞次数从多到少展示，取前10条
- **统计周期**: 最近7天内的回答采用情况
- **排序规则**: 按被采用次数降序，再按采用率降序
- **实现方式**:
  ```sql
  SELECT 
    u.id as user_id,
    u.username,
    u.avatar,
    COUNT(a.id) as adopted_count,
    COUNT(total_a.id) as total_answers,
    ROUND(COUNT(a.id) * 100.0 / COUNT(total_a.id), 2) as adoption_rate
  FROM users u
  LEFT JOIN answers a ON u.id = a.user_id 
    AND a.is_adopted = TRUE 
    AND a.create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
  LEFT JOIN answers total_a ON u.id = total_a.user_id 
    AND total_a.create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
  WHERE total_a.id IS NOT NULL
  GROUP BY u.id
  HAVING adopted_count > 0
  ORDER BY adopted_count DESC, adoption_rate DESC
  LIMIT 10
  ```

## 数据更新机制

### 实时更新字段
- `answer_count`: 新增回答时+1，删除回答时-1
- `like_count`: 点赞/取消点赞时更新
- `view_count`: 访问问题详情页时+1
- `last_answer_time`: 新增回答时更新为当前时间

### 定时任务建议
- **每日一问更新**: 每日凌晨自动选择推荐问题
- **热度分数计算**: 每小时更新一次热度分数
- **统计数据刷新**: 每日统计数据汇总
- **缓存失效**: 配合Redis设置合理的缓存过期时间

## 联系信息

如有疑问或需要技术支持，请联系前端开发团队。

**文档版本**: v2.0  
**最后更新**: 2024年1月  
**维护人员**: 前端开发团队

## 更新日志

### v2.0 (2024年1月)
- 新增问答相关接口预留
- 添加每日一问、热门回答、精选问答、问答采用榜功能
- 完善数据库表结构设计
- 添加查询规则和实现建议

### v1.0 (2024年1月)
- 初始版本
- 轮播图和推广图片接口预留
- 基础API架构搭建
