package admin

import (
	"bbs-go/internal/models"
	"bbs-go/internal/services"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/mlogclub/simple/sqls"
	"github.com/mlogclub/simple/web"
	"github.com/mlogclub/simple/web/params"
)

type DownloadController struct {
	Ctx iris.Context
}

// 资料列表
func (c *DownloadController) GetList() *web.JsonResult {
	page := params.FormValueIntDefault(c.Ctx, "page", 1)
	limit := params.FormValueIntDefault(c.Ctx, "limit", 20)
	title := params.FormValue(c.Ctx, "title")
	category := params.FormValue(c.Ctx, "category")

	cnd := sqls.NewCnd().Page(page, limit).Desc("id")
	if title != "" {
		cnd.Like("title", "%"+title+"%")
	}
	if category != "" {
		cnd.Eq("category", category)
	}

	list, paging := services.DownloadService.FindPageByCnd(cnd)
	return web.JsonPageData(list, paging)
}

// 获取资料详情
func (c *DownloadController) GetBy(id int64) *web.JsonResult {
	download := services.DownloadService.Get(id)
	if download == nil {
		return web.JsonErrorMsg("资料不存在")
	}
	return web.JsonData(download)
}

// 创建资料
func (c *DownloadController) PostCreate() *web.JsonResult {
	t := &models.Download{}
	err := params.ReadJSON(c.Ctx, t)
	if err != nil {
		return web.JsonError(err)
	}

	t.CreateTime = time.Now()
	t.UpdateTime = time.Now()
	err = services.DownloadService.Create(t)
	if err != nil {
		return web.JsonError(err)
	}
	return web.JsonData(t)
}

// 删除请求结构体
type DeleteRequest struct {
	Id int64 `json:"id"`
}

// 更新资料
func (c *DownloadController) Put() *web.JsonResult {
	var req struct {
		Id          int64  `json:"id"`
		Title       string `json:"title"`
		Description string `json:"description"`
		Category    string `json:"category"`
		FileUrl     string `json:"fileUrl"`
		FileSize    int64  `json:"fileSize"`
		Status      int    `json:"status"`
	}

	err := params.ReadJSON(c.Ctx, &req)
	if err != nil {
		return web.JsonError(err)
	}

	t := services.DownloadService.Get(req.Id)
	if t == nil {
		return web.JsonErrorMsg("资料不存在")
	}

	t.Title = req.Title
	t.Description = req.Description
	t.Category = req.Category
	t.FileUrl = req.FileUrl
	t.FileSize = req.FileSize
	t.Status = req.Status
	t.UpdateTime = time.Now()

	err = services.DownloadService.Update(t)
	if err != nil {
		return web.JsonError(err)
	}
	return web.JsonData(t)
}

// 删除资料
func (c *DownloadController) PostDelete() *web.JsonResult {
	var req DeleteRequest
	err := params.ReadJSON(c.Ctx, &req)
	if err != nil {
		return web.JsonError(err)
	}

	services.DownloadService.Delete(req.Id)
	return web.JsonSuccess()
}

// 获取下载统计
func (c *DownloadController) GetStats() *web.JsonResult {
	// 统计资料总数
	totalCount := services.DownloadService.Count(sqls.NewCnd())

	// 统计下载总次数
	var totalDownloads int64
	sqls.DB().Model(&models.Download{}).Select("sum(download_count) as total_downloads").Scan(&totalDownloads)

	// 统计各分类数量
	var categoryStats []map[string]interface{}
	rows, err := sqls.DB().Model(&models.Download{}).
		Select("category, count(*) as count, sum(download_count) as downloads").
		Group("category").
		Order("count desc").
		Rows()

	if err == nil {
		defer rows.Close()
		for rows.Next() {
			var category string
			var count, downloads int64
			rows.Scan(&category, &count, &downloads)
			categoryStats = append(categoryStats, map[string]interface{}{
				"category":  category,
				"name":      getCategoryName(category),
				"count":     count,
				"downloads": downloads,
			})
		}
	}

	return web.NewEmptyRspBuilder().
		Put("totalCount", totalCount).
		Put("totalDownloads", totalDownloads).
		Put("categoryStats", categoryStats).
		JsonResult()
}

// 获取分类名称
func getCategoryName(key string) string {
	categoryMap := map[string]string{
		"golang":   "Go语言",
		"frontend": "前端开发",
		"backend":  "后端开发",
		"database": "数据库",
		"devops":   "DevOps",
		"ai":       "人工智能",
	}

	if name, ok := categoryMap[key]; ok {
		return name
	}
	return key
}
