/* 主题相关样式 */

/* 亮色主题（默认） */
:root {
  --editor-bg: white;
  --editor-border: #e5e7eb;
  --editor-text: #374151;
  --editor-placeholder:rgb(100, 100, 100);
  --editor-toolbar-bg: #ffffff;
  --editor-hover: #f3f4f6;
  --editor-focus: #60a5fa;
  --editor-selection: rgba(200, 200, 255, 0.4);
  --editor-table-header: #f9fafb;
  --editor-table-border: #e5e7eb;
  --editor-scrollbar-thumb: #c1c1c1;
  --editor-scrollbar-track: #f1f1f1;
  
  /* 新增排版相关变量 */
  --editor-link-color: #2563eb;
  --editor-code-bg: #f1f5f9;
  --editor-code-text: #e11d48;
  --editor-blockquote-border: #60a5fa;
  --editor-blockquote-bg: rgba(96, 165, 250, 0.05);
  --editor-shadow: rgba(0, 0, 0, 0.1);
  --editor-shadow-hover: rgba(0, 0, 0, 0.15);
}

/* 暗色主题 */
@media (prefers-color-scheme: dark) {
  :root {
    --editor-bg: #2d2d2d;
    --editor-border: #4a4a4a;
    --editor-text: #e5e7eb;
    --editor-placeholder: #6b7280;
    --editor-toolbar-bg: #2d2d2d;
    --editor-hover: rgba(96, 165, 250, 0.2);
    --editor-focus: #3b82f6;
    --editor-selection: rgba(100, 100, 200, 0.4);
    --editor-table-header: #2d2d2d;
    --editor-table-border: #4a4a4a;
    --editor-scrollbar-thumb: #4a4a4a;
    --editor-scrollbar-track: #2d2d2d;
    
    /* 暗色主题排版变量 */
    --editor-link-color: #60a5fa;
    --editor-code-bg: #374151;
    --editor-code-text: #fbbf24;
    --editor-blockquote-border: #60a5fa;
    --editor-blockquote-bg: rgba(96, 165, 250, 0.1);
    --editor-shadow: rgba(0, 0, 0, 0.3);
    --editor-shadow-hover: rgba(0, 0, 0, 0.4);
  }
}

/* 手动设置暗色主题的类 */
html.dark, html.theme-dark {
  --editor-bg: #2d2d2d;
  --editor-border: #4a4a4a;
  --editor-text: #e5e7eb;
  --editor-placeholder: #6b7280;
  --editor-toolbar-bg: #2d2d2d;
  --editor-hover: rgba(96, 165, 250, 0.2);
  --editor-focus: #3b82f6;
  --editor-selection: rgba(100, 100, 200, 0.4);
  --editor-table-header: #2d2d2d;
  --editor-table-border: #4a4a4a;
  --editor-scrollbar-thumb: #4a4a4a;
  --editor-scrollbar-track: #2d2d2d;
  
  /* 暗色主题排版变量 */
  --editor-link-color: #60a5fa;
  --editor-code-bg: #374151;
  --editor-code-text: #fbbf24;
  --editor-blockquote-border: #60a5fa;
  --editor-blockquote-bg: rgba(96, 165, 250, 0.1);
  --editor-shadow: rgba(0, 0, 0, 0.3);
  --editor-shadow-hover: rgba(0, 0, 0, 0.4);
} 