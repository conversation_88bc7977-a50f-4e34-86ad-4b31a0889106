<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文章列表 - BBS-GO 技术论坛</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');
        body {
            font-family: 'Noto Sans SC', sans-serif;
        }
        .article-item:not(:last-child) {
            border-bottom: 1px solid #e5e7eb;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
        <div class="container mx-auto px-4 flex items-center justify-between h-16">
            <!-- 左侧导航 -->
            <div class="flex items-center space-x-8">
                <a href="index.html" class="flex items-center space-x-2">
                    <img src="./images/logo.svg" alt="Logo" class="h-8 w-8">
                    <span class="font-bold text-xl hidden sm:block">BBS-GO</span>
                </a>
                <nav class="hidden md:flex space-x-6">
                    <a href="index.html" class="text-gray-500 hover:text-gray-900">话题</a>
                    <a href="articles.html" class="text-gray-900 font-medium">文章</a>
                    <a href="resources.html" class="text-gray-500 hover:text-gray-900">资料下载</a>
                    <a href="#" class="text-gray-500 hover:text-gray-900">BBS-GO</a>
                    <a href="#" class="text-gray-500 hover:text-gray-900">GitHub</a>
                    <a href="#" class="text-gray-500 hover:text-gray-900">Gitee</a>
                </nav>
            </div>

            <!-- 右侧功能区 -->
            <div class="flex items-center space-x-4">
                <!-- 搜索框 -->
                <div class="relative hidden md:block">
                    <input type="text" placeholder="搜索文章..." class="bg-gray-100 rounded-full py-2 px-4 pl-10 w-64 focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white">
                    <img src="./images/search.svg" alt="搜索" class="absolute left-3 top-2.5 h-5 w-5 text-gray-400">
                </div>

                <!-- 发表按钮 -->
                <button class="bg-blue-500 hover:bg-blue-600 text-white rounded-full px-4 py-2 flex items-center">
                    <img src="./images/plus.svg" alt="发表" class="h-5 w-5 mr-1 text-white">
                    <span>发表</span>
                </button>

                <!-- 消息通知 -->
                <a href="#" class="text-gray-500 hover:text-gray-900">
                    <img src="./images/message.svg" alt="消息" class="h-6 w-6">
                </a>

                <!-- 用户头像 -->
                <a href="#" class="flex items-center space-x-2">
                    <img src="https://via.placeholder.com/32" alt="用户头像" class="h-8 w-8 rounded-full">
                    <span class="hidden md:block">春光</span>
                </a>

                <!-- 暗色模式切换 -->
                <button class="text-gray-500 hover:text-gray-900">
                    <img src="./images/moon.svg" alt="暗色模式" class="h-6 w-6">
                </button>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="container mx-auto px-4 py-6 flex flex-col md:flex-row gap-6">
        <!-- 左侧边栏 -->
        <aside class="w-full md:w-64 bg-white rounded-lg shadow-sm p-4">
            <div class="flex flex-col space-y-2">
                <div class="text-lg font-medium mb-2">文章分类</div>
                
                <a href="#" class="flex items-center space-x-3 p-2 bg-gray-100 rounded-lg">
                    <span>全部文章</span>
                </a>
                
                <a href="#" class="flex items-center space-x-3 p-2 hover:bg-gray-100 rounded-lg">
                    <span>技术分享</span>
                </a>
                
                <a href="#" class="flex items-center space-x-3 p-2 hover:bg-gray-100 rounded-lg">
                    <span>项目实战</span>
                </a>
                
                <a href="#" class="flex items-center space-x-3 p-2 hover:bg-gray-100 rounded-lg">
                    <span>开发工具</span>
                </a>
                
                <a href="#" class="flex items-center space-x-3 p-2 hover:bg-gray-100 rounded-lg">
                    <span>架构设计</span>
                </a>
                
                <a href="#" class="flex items-center space-x-3 p-2 hover:bg-gray-100 rounded-lg">
                    <span>学习笔记</span>
                </a>
            </div>
            
            <!-- 热门标签 -->
            <div class="mt-8">
                <h3 class="text-lg font-medium mb-3">热门标签</h3>
                <div class="flex flex-wrap gap-2">
                    <a href="#" class="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm">Go</a>
                    <a href="#" class="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm">Vue</a>
                    <a href="#" class="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm">emoji</a>
                    <a href="#" class="px-3 py-1 bg-yellow-100 text-yellow-700 rounded-full text-sm">uni-app</a>
                    <a href="#" class="px-3 py-1 bg-red-100 text-red-700 rounded-full text-sm">Harmony</a>
                    <a href="#" class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">Excel</a>
                    <a href="#" class="px-3 py-1 bg-indigo-100 text-indigo-700 rounded-full text-sm">开源项目</a>
                </div>
            </div>
            
            <!-- 最新签到 -->
            <div class="mt-8">
                <div class="bg-blue-50 rounded-lg p-4">
                    <h3 class="text-lg font-medium mb-2 text-blue-800">签到</h3>
                    <p class="text-blue-600 text-sm mb-3">连续签到获得更多积分奖励</p>
                    <button class="w-full bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600">立即签到</button>
                </div>
            </div>
        </aside>

        <!-- 右侧主内容 -->
        <div class="flex-1">
            <!-- 文章列表 -->
            <div class="bg-white rounded-lg shadow-sm">
                <!-- 文章项 1 -->
                <div class="p-6 article-item">
                    <div class="flex items-start space-x-4">
                        <img src="https://via.placeholder.com/48" alt="马浩然的头像" class="h-12 w-12 rounded-full">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                                <h3 class="font-medium">马浩然</h3>
                                <span class="text-gray-500 text-sm">发布于 2025-02-08 10:15</span>
                            </div>
                            <a href="post-detail.html" class="block hover:text-blue-600">
                                <h2 class="text-xl font-bold mb-3">手撸一个emoji表情包组件</h2>
                                <p class="text-gray-600 mb-4">我一找一开始在网上找成品emoji表情包组件，花了两个小时没有找到自己理想的，要么加载太慢图片不好看甚至不清楚....自省自力为了满足自己的需求以及节约时间，打算半小时自己写一个，上代码 Emoji.vue &lt;template&gt; &lt;div class="container"&gt; &lt;div class="content"&gt; &lt;div class="content-title" v-if="use_list_length"&gt;常用&lt;/div&gt; &lt;ul class="emoji...</p>
                            </a>
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs">emoji</span>
                                <span class="px-2 py-1 bg-green-100 text-green-700 rounded text-xs">vue</span>
                            </div>
                            <div class="flex items-center space-x-6 text-gray-500 text-sm">
                                <div class="flex items-center space-x-1">
                                    <img src="./images/eye.svg" alt="浏览" class="h-4 w-4">
                                    <span>1,234</span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <img src="./images/thumbs-up.svg" alt="点赞" class="h-4 w-4">
                                    <span>42</span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <img src="./images/comment.svg" alt="评论" class="h-4 w-4">
                                    <span>18</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 文章项 2 -->
                <div class="p-6 article-item">
                    <div class="flex items-start space-x-4">
                        <img src="https://via.placeholder.com/48" alt="hong的头像" class="h-12 w-12 rounded-full">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                                <h3 class="font-medium">hong</h3>
                                <span class="text-gray-500 text-sm">发布于 2024-11-22 10:52</span>
                            </div>
                            <a href="post-detail.html" class="block hover:text-blue-600">
                                <h2 class="text-xl font-bold mb-3">《uni-app for Harmony 的朝阳天下的最佳实践》</h2>
                                <p class="text-gray-600 mb-4">《uni-app for Harmony 的朝阳天下的最佳实践》在移动应用开发中，提供一个美观且功能强大的朝阳展示界面是吸引用户体验的关键。本文将深入介绍一个基于 uni-app for Harmony 开发的朝阳展示页面实现。一、开发初衷随着 1.鸿蒙 uni-app 的朝阳系统特性，注入了解 uni-app 的朝阳平台开发者，组件库...</p>
                            </a>
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="px-2 py-1 bg-yellow-100 text-yellow-700 rounded text-xs">uni-app</span>
                                <span class="px-2 py-1 bg-red-100 text-red-700 rounded text-xs">Harmony</span>
                                <span class="px-2 py-1 bg-purple-100 text-purple-700 rounded text-xs">移动开发</span>
                            </div>
                            <div class="flex items-center space-x-6 text-gray-500 text-sm">
                                <div class="flex items-center space-x-1">
                                    <img src="./images/eye.svg" alt="浏览" class="h-4 w-4">
                                    <span>856</span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <img src="./images/thumbs-up.svg" alt="点赞" class="h-4 w-4">
                                    <span>28</span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <img src="./images/comment.svg" alt="评论" class="h-4 w-4">
                                    <span>12</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 文章项 3 -->
                <div class="p-6 article-item">
                    <div class="flex items-start space-x-4">
                        <img src="https://via.placeholder.com/48" alt="xurl的头像" class="h-12 w-12 rounded-full">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                                <h3 class="font-medium">xurl</h3>
                                <span class="text-gray-500 text-sm">发布于 2024-10-17 23:19</span>
                            </div>
                            <a href="post-detail.html" class="block hover:text-blue-600">
                                <h2 class="text-xl font-bold mb-3">Excelizr 开源基础库 2.9.0 版本正式发布</h2>
                                <div class="flex items-start space-x-4 mb-4">
                                    <div class="flex-1">
                                        <p class="text-gray-600 mb-4">Excelizr 是 Go 语言编写的用于操作 Office Excel 文档基础库，基于 ECMA-376，ISO/IEC 29500 国际标准。可以使用它来读取、写入由 Microsoft Excel™ 2007 以来版本创建的电子表格文档。支持 XLAM / XLSM / XLSX / XLTM / XLTX 等多种文档格式，高度兼容带有样式、图片(表)、透视表、切片器、图表与公式等复杂组件的文档，并提供流式读取API，用于处理包含大规模数据的工作表，可应用于各类报表平台、云计算系统和企业信息管理系统等。</p>
                                    </div>
                                    <img src="https://via.placeholder.com/120x80" alt="Excelizr" class="rounded-lg">
                                </div>
                            </a>
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs">Excelizr</span>
                                <span class="px-2 py-1 bg-green-100 text-green-700 rounded text-xs">开源项目</span>
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">Go语言</span>
                            </div>
                            <div class="flex items-center space-x-6 text-gray-500 text-sm">
                                <div class="flex items-center space-x-1">
                                    <img src="./images/eye.svg" alt="浏览" class="h-4 w-4">
                                    <span>2,145</span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <img src="./images/thumbs-up.svg" alt="点赞" class="h-4 w-4">
                                    <span>89</span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <img src="./images/comment.svg" alt="评论" class="h-4 w-4">
                                    <span>35</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 文章项 4 -->
                <div class="p-6 article-item">
                    <div class="flex items-start space-x-4">
                        <img src="https://via.placeholder.com/48" alt="望夏的头像" class="h-12 w-12 rounded-full">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                                <h3 class="font-medium">望夏</h3>
                                <span class="text-gray-500 text-sm">发布于 2024-09-05 12:51</span>
                            </div>
                            <a href="post-detail.html" class="block hover:text-blue-600">
                                <h2 class="text-xl font-bold mb-3">《uni-app for Harmony 的朝阳天下的最佳实践》</h2>
                                <p class="text-gray-600 mb-4">《uni-app for Harmony 的朝阳天下的最佳实践》在移动应用开发中，提供一个美观且功能强大的朝阳展示界面是吸引用户体验的关键。本文将深入介绍一个基于 uni-app for Harmony 开发的朝阳展示页面实现。一、开发初衷随着 1.鸿蒙 uni-app 的朝阳系统特性，注入了解 uni-app 的朝阳平台开发者，组件库...</p>
                            </a>
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="px-2 py-1 bg-yellow-100 text-yellow-700 rounded text-xs">uni-app</span>
                                <span class="px-2 py-1 bg-red-100 text-red-700 rounded text-xs">Harmony</span>
                            </div>
                            <div class="flex items-center space-x-6 text-gray-500 text-sm">
                                <div class="flex items-center space-x-1">
                                    <img src="./images/eye.svg" alt="浏览" class="h-4 w-4">
                                    <span>1,567</span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <img src="./images/thumbs-up.svg" alt="点赞" class="h-4 w-4">
                                    <span>73</span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <img src="./images/comment.svg" alt="评论" class="h-4 w-4">
                                    <span>26</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 文章项 5 -->
                <div class="p-6 article-item">
                    <div class="flex items-start space-x-4">
                        <img src="https://via.placeholder.com/48" alt="GoFly全栈开发的头像" class="h-12 w-12 rounded-full">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                                <h3 class="font-medium">GoFly全栈开发</h3>
                                <span class="text-gray-500 text-sm">发布于 2024-08-22 16:46</span>
                            </div>
                            <a href="post-detail.html" class="block hover:text-blue-600">
                                <h2 class="text-xl font-bold mb-3">分享一个最近完成的项目打包套件框架：百度地图路径实况历史轨迹回放、轨迹回放速度、整合点、自定义弹框和实时监控视频、多路视频实况播放</h2>
                                <div class="flex items-start space-x-4 mb-4">
                                    <div class="flex-1">
                                        <p class="text-gray-600 mb-4">前言 分享一个网络项目开发技术，一个车辆行驶轨迹监控，行车视频监控，对接车载实时监控台等 功能，本文重点解决，相关技术要点和实施监控台等项目，监控台等相关实时监控技术，监控台等实施技术点的监控台等，车载监控的整合点，可以提供监控回放功能，特别是监控回放速度，(特别专业，需要时间的监控，可以提供监控回放速度，(特别...</p>
                                    </div>
                                    <img src="https://via.placeholder.com/120x80" alt="地图轨迹" class="rounded-lg">
                                </div>
                            </a>
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs">Go语言</span>
                                <span class="px-2 py-1 bg-green-100 text-green-700 rounded text-xs">百度地图</span>
                                <span class="px-2 py-1 bg-purple-100 text-purple-700 rounded text-xs">实时监控</span>
                                <span class="px-2 py-1 bg-yellow-100 text-yellow-700 rounded text-xs">轨迹回放</span>
                            </div>
                            <div class="flex items-center space-x-6 text-gray-500 text-sm">
                                <div class="flex items-center space-x-1">
                                    <img src="./images/eye.svg" alt="浏览" class="h-4 w-4">
                                    <span>945</span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <img src="./images/thumbs-up.svg" alt="点赞" class="h-4 w-4">
                                    <span>52</span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <img src="./images/comment.svg" alt="评论" class="h-4 w-4">
                                    <span>19</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 分页 -->
            <div class="mt-8 flex justify-center">
                <nav class="inline-flex rounded-md shadow">
                    <a href="#" class="py-2 px-4 bg-white border border-gray-300 rounded-l-md text-gray-500 hover:bg-gray-50">上一页</a>
                    <a href="#" class="py-2 px-4 bg-blue-500 border border-blue-500 text-white">1</a>
                    <a href="#" class="py-2 px-4 bg-white border border-gray-300 text-gray-500 hover:bg-gray-50">2</a>
                    <a href="#" class="py-2 px-4 bg-white border border-gray-300 text-gray-500 hover:bg-gray-50">3</a>
                    <a href="#" class="py-2 px-4 bg-white border border-gray-300 rounded-r-md text-gray-500 hover:bg-gray-50">下一页</a>
                </nav>
            </div>
        </div>
        
        <!-- 右侧边栏 -->
        <div class="w-full md:w-80 space-y-6">
            <!-- 公告 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-medium mb-4">公告</h3>
                <div class="space-y-3">
                    <div class="p-3 bg-blue-50 rounded-lg">
                        <p class="text-sm text-blue-800">欢迎访问 BBS-GO交流社区，点击这里查看使用的详情</p>
                        <p class="text-xs text-blue-600 mt-1">可以通过发布帖子、回复帖子等形式，不断社区积分一等奖。</p>
                    </div>
                </div>
                <button class="mt-3 text-blue-600 text-sm hover:underline">立即签到</button>
            </div>
            
            <!-- 积分排行 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-medium mb-4">积分排行</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="flex items-center justify-center w-6 h-6 bg-yellow-500 text-white text-xs font-bold rounded-full">1</div>
                            <img src="https://via.placeholder.com/32" alt="大喵喵酱" class="h-8 w-8 rounded-full">
                            <div>
                                <div class="font-medium text-sm">大喵喵酱</div>
                                <div class="text-xs text-gray-500">579 积分 • 1449 评论</div>
                            </div>
                        </div>
                        <span class="text-sm text-gray-500">3968</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="flex items-center justify-center w-6 h-6 bg-gray-400 text-white text-xs font-bold rounded-full">2</div>
                            <img src="https://via.placeholder.com/32" alt="黄小可" class="h-8 w-8 rounded-full">
                            <div>
                                <div class="font-medium text-sm">黄小可</div>
                                <div class="text-xs text-gray-500">138 积分 • 65 评论</div>
                            </div>
                        </div>
                        <span class="text-sm text-gray-500">1493</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="flex items-center justify-center w-6 h-6 bg-orange-500 text-white text-xs font-bold rounded-full">3</div>
                            <img src="https://via.placeholder.com/32" alt="泰明星" class="h-8 w-8 rounded-full">
                            <div>
                                <div class="font-medium text-sm">泰明星</div>
                                <div class="text-xs text-gray-500">105 积分 • 188 评论</div>
                            </div>
                        </div>
                        <span class="text-sm text-gray-500">1192</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="flex items-center justify-center w-6 h-6 bg-gray-300 text-gray-600 text-xs font-bold rounded-full">4</div>
                            <img src="https://via.placeholder.com/32" alt="Akira" class="h-8 w-8 rounded-full">
                            <div>
                                <div class="font-medium text-sm">Akira</div>
                                <div class="text-xs text-gray-500">47 积分 • 54 评论</div>
                            </div>
                        </div>
                        <span class="text-sm text-gray-500">819</span>
                    </div>
                </div>
                <div class="mt-4 text-center">
                    <a href="#" class="text-blue-600 text-sm hover:underline">查看更多 →</a>
                </div>
            </div>
            
            <!-- 友情链接 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-medium mb-4">友情链接</h3>
                <div class="space-y-2">
                    <a href="#" class="block text-blue-600 hover:underline text-sm">查看更多 ></a>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t mt-8 py-8">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between">
                <div class="mb-6 md:mb-0">
                    <div class="flex items-center space-x-2">
                        <img src="./images/logo.svg" alt="Logo" class="h-8 w-8">
                        <span class="font-bold text-xl">BBS-GO</span>
                    </div>
                    <p class="text-gray-600 mt-2">基于Go语言开发的现代化社区系统</p>
                </div>
                
                <div class="grid grid-cols-2 md:grid-cols-3 gap-8">
                    <div>
                        <h3 class="font-medium mb-2">产品</h3>
                        <ul class="space-y-2 text-gray-600">
                            <li><a href="#" class="hover:text-gray-900">功能特性</a></li>
                            <li><a href="#" class="hover:text-gray-900">安装指南</a></li>
                            <li><a href="#" class="hover:text-gray-900">API文档</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="font-medium mb-2">资源</h3>
                        <ul class="space-y-2 text-gray-600">
                            <li><a href="#" class="hover:text-gray-900">开发者社区</a></li>
                            <li><a href="#" class="hover:text-gray-900">帮助中心</a></li>
                            <li><a href="#" class="hover:text-gray-900">贡献指南</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="font-medium mb-2">关于</h3>
                        <ul class="space-y-2 text-gray-600">
                            <li><a href="#" class="hover:text-gray-900">关于我们</a></li>
                            <li><a href="#" class="hover:text-gray-900">联系方式</a></li>
                            <li><a href="#" class="hover:text-gray-900">隐私政策</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="border-t mt-8 pt-6 text-center text-gray-600">
                <p>© 2025 BBS-GO. 保留所有权利。</p>
            </div>
        </div>
    </footer>

    <script>
        // 暗色模式切换
        function toggleDarkMode() {
            document.documentElement.classList.toggle('dark');
        }
    </script>
</body>
</html> 