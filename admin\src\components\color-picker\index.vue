<template>
  <div class="color-picker-container">
    <div
      class="color-display"
      :style="{ backgroundColor: modelValue || '#FFFFFF' }"
      @click="showPicker = !showPicker"
    ></div>
    <a-input
      :model-value="modelValue"
      placeholder="请输入颜色值"
      allow-clear
      @update:model-value="updateValue"
    />
    <div v-if="showPicker" class="color-picker-dropdown">
      <div class="color-picker-header">
        <span>选择颜色</span>
        <a-button type="text" size="mini" @click="showPicker = false">
          <icon-close />
        </a-button>
      </div>
      <div class="color-palette">
        <div
          v-for="color in colorPalette"
          :key="color"
          class="color-item"
          :style="{ backgroundColor: color }"
          @click="selectColor(color)"
        ></div>
      </div>
      <a-input
        :model-value="modelValue"
        placeholder="#RRGGBB"
        allow-clear
        @update:model-value="updateValue"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits, watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:modelValue']);

const showPicker = ref(false);

const colorPalette = [
  '#FFFFFF', '#F5F5F5', '#D9D9D9', '#BFBFBF', '#8C8C8C', '#595959', '#262626', '#000000',
  '#F5222D', '#FA541C', '#FA8C16', '#FAAD14', '#FADB14', '#A0D911', '#52C41A', '#13C2C2',
  '#1677FF', '#2F54EB', '#722ED1', '#EB2F96', '#FFA39E', '#FFD591', '#FFFB8F', '#B7EB8F',
  '#87E8DE', '#91D5FF', '#ADC6FF', '#D3ADF7',
];

const updateValue = (value: string) => {
  emit('update:modelValue', value);
};

const selectColor = (color: string) => {
  emit('update:modelValue', color);
  showPicker.value = false;
};

// 点击外部关闭颜色选择器
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (!target.closest('.color-picker-container')) {
    showPicker.value = false;
  }
};

// 挂载和卸载事件监听
watch(
  () => showPicker.value,
  (val) => {
    if (val) {
      setTimeout(() => {
        document.addEventListener('click', handleClickOutside);
      }, 0);
    } else {
      document.removeEventListener('click', handleClickOutside);
    }
  }
);
</script>

<style scoped>
.color-picker-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.color-display {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  margin-right: 8px;
  border: 1px solid #d9d9d9;
  cursor: pointer;
}

.color-picker-dropdown {
  position: absolute;
  top: 40px;
  left: 0;
  width: 240px;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  padding: 12px;
}

.color-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.color-palette {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 6px;
  margin-bottom: 12px;
}

.color-item {
  width: 20px;
  height: 20px;
  border-radius: 2px;
  cursor: pointer;
  border: 1px solid #d9d9d9;
  transition: transform 0.2s;
}

.color-item:hover {
  transform: scale(1.1);
}
</style> 