name: bbs-go-server

on: [ push ]

jobs:

  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2

      - name: Set up Go
        uses: actions/setup-go@v2
        with:
          go-version: 1.21

      - name: Install dependencies
        working-directory: ./server
        run: |
          go mod download

      - name: Build
        working-directory: ./server
        run: |
          make build
