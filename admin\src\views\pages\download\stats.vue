<template>
  <div class="container">
    <Breadcrumb :items="['资料下载', '下载统计']" />
    
    <a-row :gutter="16">
      <!-- 统计卡片 -->
      <a-col :span="24" :lg="8">
        <a-card class="general-card" title="资料总览">
          <a-spin :loading="loading">
            <a-row :gutter="[16, 16]">
              <a-col :span="12">
                <a-statistic
                  title="资料总数"
                  :value="statsData.totalCount"
                  show-group-separator
                >
                  <template #suffix>
                    <icon-file style="color: #3370ff" />
                  </template>
                </a-statistic>
              </a-col>
              <a-col :span="12">
                <a-statistic
                  title="下载总次数"
                  :value="statsData.totalDownloads"
                  show-group-separator
                >
                  <template #suffix>
                    <icon-download style="color: #ee4d38" />
                  </template>
                </a-statistic>
              </a-col>
            </a-row>
            
            <a-divider />
            
            <a-typography-title :heading="6" style="margin-bottom: 16px">
              分类统计
            </a-typography-title>
            
            <a-table
              :data="statsData.categoryStats"
              :pagination="false"
              :bordered="false"
            >
              <template #columns>
                <a-table-column title="分类" data-index="name" />
                <a-table-column title="资料数量" data-index="count" />
                <a-table-column title="下载次数" data-index="downloads" />
                <a-table-column title="占比" align="right">
                  <template #cell="{ record }">
                    <a-progress
                      :percent="Math.round((record.downloads / statsData.totalDownloads) * 100)"
                      :show-text="true"
                      :stroke-width="4"
                    />
                  </template>
                </a-table-column>
              </template>
            </a-table>
          </a-spin>
        </a-card>
      </a-col>
      
      <!-- 图表 -->
      <a-col :span="24" :lg="16">
        <a-card class="general-card" title="下载趋势">
          <a-spin :loading="loading">
            <div ref="chartRef" class="chart-container"></div>
          </a-spin>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue';
import { Message } from '@arco-design/web-vue';
import { getDownloadStats } from '@/api/download';
import type { DownloadStatsData } from '@/api/download';
import * as echarts from 'echarts';

// 状态变量
const loading = ref(false);
const chartRef = ref<HTMLElement>();
let chart: echarts.ECharts | null = null;

// 统计数据
const statsData = reactive<DownloadStatsData>({
  totalCount: 0,
  totalDownloads: 0,
  categoryStats: [],
});

// 获取统计数据
const fetchStats = async () => {
  try {
    loading.value = true;
    const response = await getDownloadStats();
    if (response) {
      Object.assign(statsData, response);
      // 更新图表
      renderChart();
    } else {
      console.error('Invalid API response format:', response);
      Message.error('获取数据失败：响应格式不正确');
    }
  } catch (err) {
    console.error(err);
    Message.error('获取统计数据失败');
  } finally {
    loading.value = false;
  }
};

// 渲染图表
const renderChart = () => {
  if (!chartRef.value) return;
  
  if (!chart) {
    chart = echarts.init(chartRef.value);
  }
  
  // 准备图表数据
  const categories = statsData.categoryStats.map(item => item.name);
  const downloadCounts = statsData.categoryStats.map(item => item.downloads);
  const resourceCounts = statsData.categoryStats.map(item => item.count);
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['资料数量', '下载次数']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: categories,
        axisLabel: {
          interval: 0,
          rotate: 30
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '下载次数',
        position: 'right'
      },
      {
        type: 'value',
        name: '资料数量',
        position: 'left'
      }
    ],
    series: [
      {
        name: '资料数量',
        type: 'bar',
        yAxisIndex: 1,
        data: resourceCounts,
        itemStyle: {
          color: '#3370ff'
        }
      },
      {
        name: '下载次数',
        type: 'bar',
        data: downloadCounts,
        itemStyle: {
          color: '#ee4d38'
        }
      }
    ]
  };
  
  chart.setOption(option);
};

// 窗口大小变化时重新调整图表大小
const handleResize = () => {
  chart?.resize();
};

// 页面加载时获取数据
onMounted(() => {
  fetchStats();
  window.addEventListener('resize', handleResize);
});

// 组件销毁前清理
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  chart?.dispose();
  chart = null;
});
</script>

<style scoped>
.container {
  padding: 0 20px 20px;
}

.chart-container {
  height: 400px;
}

:deep(.arco-table-th) {
  background-color: var(--color-fill-2);
}
</style> 