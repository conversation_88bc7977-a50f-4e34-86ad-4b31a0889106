const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// 定义要修复的文件或目录
const targets = [
  'src/components/FileUpload.vue',
  'src/views/pages/download/index.vue'
];

console.log('🔧 开始修复ESLint问题...');

try {
  // 对每个目标运行ESLint修复
  targets.forEach(target => {
    const fullPath = path.resolve(__dirname, target);
    
    if (fs.existsSync(fullPath)) {
      console.log(`修复文件: ${target}`);
      execSync(`npx eslint --fix "${fullPath}"`, { stdio: 'inherit' });
    } else {
      console.warn(`⚠️ 文件不存在: ${target}`);
    }
  });
  
  console.log('✅ ESLint修复完成!');
} catch (error) {
  console.error('❌ ESLint修复失败:', error.message);
  process.exit(1);
} 