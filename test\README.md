# BBS-GO 技术论坛界面

这是一个使用 Tailwind CSS 和 HTML 实现的技术论坛界面，基于 BBS-GO 项目设计。

## 功能特点

- 响应式设计，适配移动端和桌面端
- 暗色模式支持
- 技术论坛特有功能元素:
  - 技术分类导航
  - 活跃用户排行榜
  - 技术资源推荐
  - 技术活动日历
  - 代码沙箱功能

## 如何使用

1. 直接在浏览器中打开 `index.html` 文件即可查看界面
2. 所有的图标都存储在 `images` 目录下
3. 页面使用 CDN 加载 Tailwind CSS，确保有网络连接

## 界面结构

- 顶部导航栏：包含网站标志、主导航、搜索框和用户功能区
- 左侧边栏：包含内容分类、技术分类导航和活跃用户排行
- 主内容区：话题列表、技术资源推荐和技术活动日历
- 页脚：网站信息和链接
- 模态框：代码沙箱功能

## 自定义

- 修改颜色主题：编辑 `<head>` 中的 Tailwind 配置
- 添加新的图标：在 `images` 目录中添加新的 SVG 文件
- 修改布局：编辑 HTML 结构和 Tailwind 类

## 注意事项

- 这是一个静态界面演示，不包含后端功能
- 所有的交互功能都是通过简单的 JavaScript 实现的
- 用户头像使用了占位图片服务，实际使用时需要替换为真实图片

## 技术栈

- HTML5
- Tailwind CSS (通过 CDN 加载)
- 少量 JavaScript 用于交互 