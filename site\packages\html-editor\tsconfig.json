{
  "include": ["env.d.ts", "src/**/*", "src/**/*.vue"],
  "exclude": ["src/**/__tests__/*"],
  "compilerOptions": {
    "composite": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    },
    "types": ["node"],
    "skipLibCheck": true,
    "noImplicitAny": false,
    "strict": false,

    "declaration": true, // 启用声明文件生成
    "declarationDir": "./types", // 指定声明文件的输出目录
    "emitDeclarationOnly": true // 仅生成声明文件，不生成 JavaScript 文件
  }
}
