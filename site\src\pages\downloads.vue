<template>
  <div class="downloads-page">
    <div class="container">
      <!-- 页面标题和描述 -->
      <div class="page-header">
        <h1 class="title">技术资料下载中心</h1>
        <p class="subtitle">这里收集了各种高质量的技术资料，供社区成员学习和参考</p>
      </div>

      <!-- 分类标签和视图切换 -->
      <div class="controls-section">
        <div class="category-tabs">
          <button 
            v-for="category in categories" 
            :key="category.key"
            :class="['tab-button', { 'is-active': activeCategory === category.key }]"
            @click="setActiveCategory(category.key)"
          >
            {{ category.name }}
          </button>
        </div>
        
        <!-- 视图切换按钮 -->
        <div class="view-toggle">
          <button 
            :class="['view-btn', { 'active': viewMode === 'grid' }]"
            @click="setViewMode('grid')"
            title="卡片视图"
          >
            <div class="grid-icon"></div>
          </button>
          <button 
            :class="['view-btn', { 'active': viewMode === 'list' }]"
            @click="setViewMode('list')"
            title="列表视图"
          >
            <div class="list-icon"></div>
          </button>
        </div>
      </div>

      <!-- 资料列表 -->
      <div :class="['downloads-container', viewMode]">
        <!-- 加载状态 -->
        <div v-if="isLoading" class="loading-container">
          <div class="loading-spinner"></div>
          <p>加载中，请稍候...</p>
        </div>
        
        <!-- 无数据状态 -->
        <div v-else-if="!filteredDownloads.length" class="empty-container">
          <div class="empty-icon">📚</div>
          <p>暂无资料</p>
          <p class="empty-tip">该分类下暂时没有资料，请选择其他分类或稍后再试</p>
        </div>
        
        <!-- 卡片视图 -->
        <div v-else-if="viewMode === 'grid'" class="downloads-grid">
          <div 
            v-for="item in filteredDownloads" 
            :key="item.id"
            class="download-card"
          >
            <div class="card-header">
              <span :class="['category-tag', item.category]">{{ getCategoryName(item.category) }}</span>
              <span class="file-size">{{ formatSize(item.fileSize) }}</span>
            </div>
            
            <h3 class="card-title">{{ item.title }}</h3>
            <p class="card-description">{{ item.description }}</p>
            
            <div class="card-footer">
              <div class="meta-info">
                <span class="download-count">下载次数: {{ item.downloadCount }}</span>
                <span class="update-date">更新于: {{ formatDate(item.updateTime) }}</span>
              </div>
              <button class="download-btn" @click="handleDownload(item)">
                <i class="iconfont icon-download"></i>
                下载
              </button>
            </div>
          </div>
        </div>

        <!-- 列表视图 -->
        <div v-else class="downloads-list">
          <div 
            v-for="item in filteredDownloads" 
            :key="item.id"
            class="download-item"
          >
            <div class="item-main">
              <div class="item-header">
                <h3 class="item-title">{{ item.title }}</h3>
                <div class="item-meta">
                  <span :class="['category-tag', item.category]">{{ getCategoryName(item.category) }}</span>
                  <span class="file-size">{{ formatSize(item.fileSize) }}</span>
                </div>
              </div>
              <p class="item-description">{{ item.description }}</p>
              <div class="item-stats">
                <span class="download-count">下载次数: {{ item.downloadCount }}</span>
                <span class="update-date">更新于: {{ formatDate(item.updateTime) }}</span>
              </div>
            </div>
            <div class="item-action">
              <button class="download-btn" @click="handleDownload(item)">
                <i class="iconfont icon-download"></i>
                下载
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="totalPages > 1" class="pagination-wrapper">
        <nav class="pagination" role="navigation" aria-label="pagination">
          <a class="pagination-previous" :class="{ 'is-disabled': currentPage === 1 }" @click="goToPage(currentPage - 1)">上一页</a>
          <a class="pagination-next" :class="{ 'is-disabled': currentPage === totalPages }" @click="goToPage(currentPage + 1)">下一页</a>
          <ul class="pagination-list">
            <li v-for="page in visiblePages" :key="page">
              <a 
                :class="['pagination-link', { 'is-current': page === currentPage }]"
                @click="goToPage(page)"
              >
                {{ page }}
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getDownloadList, getDownloadCategories, downloadFile, formatFileSize } from '~/apis/download';

// 页面元数据
definePageMeta({
  title: '技术资料下载中心'
});

// 路由对象
const route = useRoute();

// 响应式数据
const activeCategory = ref('all');
const currentPage = ref(1);
const pageSize = ref(12);
const viewMode = ref('grid'); // 'grid' 或 'list'
const isLoading = ref(false);
const downloadData = ref({
  list: [],
  paging: {
    page: 1,
    limit: 12,
    total: 0
  }
});
const categories = ref([
  { key: 'all', name: '全部' },
  { key: 'edge', name: '边缘计算' },
  { key: 'deep', name: '深度学习' },
  { key: 'ai', name: 'AI算法' },
  { key: 'robot', name: '机器人' },
  { key: 'other', name: '其他' }
]);

// 获取分类列表
async function fetchCategories() {
  try {
    const { data } = await getDownloadCategories();
    if (data.value) {
      categories.value = data.value;
    }
  } catch (error) {
    console.error('获取分类失败:', error);
  }
}

// 获取下载资料列表
async function fetchDownloads() {
  isLoading.value = true;
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      category: activeCategory.value === 'all' ? '' : activeCategory.value
    };
    
    const { data } = await getDownloadList(params);
    if (data.value) {
      downloadData.value = {
        list: data.value.results || [],
        paging: {
          page: data.value.page || 1,
          limit: data.value.limit || pageSize.value,
          total: data.value.total || 0
        }
      };
    }
  } catch (error) {
    console.error('获取资料列表失败:', error);
  } finally {
    isLoading.value = false;
  }
}

// 计算属性
const filteredDownloads = computed(() => {
  return downloadData.value.list;
});

const totalPages = computed(() => {
  const { total, limit } = downloadData.value.paging;
  return Math.ceil(total / limit);
});

const visiblePages = computed(() => {
  const pages = [];
  const start = Math.max(1, currentPage.value - 2);
  const end = Math.min(totalPages.value, currentPage.value + 2);
  
  for (let i = start; i <= end; i++) {
    pages.push(i);
  }
  return pages;
});

// 方法
function setActiveCategory(category) {
  activeCategory.value = category;
  currentPage.value = 1; // 切换分类时重置到第一页
  fetchDownloads();
}

function getCategoryName(categoryKey) {
  const category = categories.value.find(cat => cat.key === categoryKey);
  return category ? category.name : categoryKey;
}

function goToPage(page) {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
    fetchDownloads();
  }
}

function setViewMode(mode) {
  viewMode.value = mode;
}

function formatSize(bytes) {
  return formatFileSize(bytes);
}

function formatDate(dateStr) {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleDateString();
}

async function handleDownload(item) {
  try {
    const { data } = await downloadFile(item.id);
    if (data.value && data.value.url) {
      // 使用浏览器打开下载链接
      window.open(data.value.url, '_blank');
    }
  } catch (error) {
    console.error('下载失败:', error);
    alert('下载失败，请稍后重试');
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchCategories();
  fetchDownloads();
});

// 监听路由参数变化
watch(
  () => route.query,
  (query) => {
    if (query.category) {
      activeCategory.value = query.category;
    }
    if (query.page) {
      currentPage.value = parseInt(query.page) || 1;
    }
    fetchDownloads();
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.downloads-page {
  min-height: 100vh;
  background-color: var(--bg-color);
  padding: 40px 0 40px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
  
  .title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 16px;
  }
  
  .subtitle {
    font-size: 1.1rem;
    color: var(--text-color2);
    margin: 0;
  }
}

.controls-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  flex-wrap: wrap;
  gap: 16px;
}

.category-tabs {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  
  .tab-button {
    padding: 8px 16px;
    border: none;
    border-radius: 20px;
    background-color: var(--card-bg-color);
    color: var(--text-color2);
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background-color: var(--hover-bg-color);
      color: var(--text-color);
    }
    
    &.is-active {
      background-color: #3b82f6;
      color: white;
    }
  }
}

.view-toggle {
  display: flex;
  gap: 4px;
  
  .view-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background-color: var(--card-bg-color);
    color: var(--text-color3);
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background-color: var(--hover-bg-color);
      color: var(--text-color);
    }
    
    &.active {
      background-color: #3b82f6;
      color: white;
      border-color: #3b82f6;
    }
    
    .grid-icon,
    .list-icon {
      width: 16px;
      height: 16px;
      position: relative;
    }
    
    .grid-icon {
      &::before,
      &::after {
        content: '';
        position: absolute;
        background-color: currentColor;
        border-radius: 1px;
      }
      
      &::before {
        top: 0;
        left: 0;
        width: 6px;
        height: 6px;
        box-shadow: 10px 0 0 currentColor, 0 10px 0 currentColor, 10px 10px 0 currentColor;
      }
    }
    
    .list-icon {
      &::before,
      &::after {
        content: '';
        position: absolute;
        background-color: currentColor;
        border-radius: 1px;
      }
      
      &::before {
        top: 2px;
        left: 0;
        width: 16px;
        height: 2px;
        box-shadow: 0 6px 0 currentColor, 0 12px 0 currentColor;
      }
    }
  }
}

.downloads-container {
  margin-bottom: 40px;
  min-height: 300px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: #3b82f6;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 16px;
  }
  
  @keyframes spin {
    to { transform: rotate(360deg); }
  }
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  p {
    margin: 0;
    color: var(--text-color2);
  }
  
  .empty-tip {
    font-size: 14px;
    margin-top: 8px;
    color: var(--text-color3);
  }
}

.downloads-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
}

.downloads-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.download-item {
  display: flex;
  align-items: flex-start;
  padding: 20px;
  background-color: var(--card-bg-color);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-color: var(--border-hover-color);
  }
  
  .item-main {
    flex: 1;
    
    .item-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;
      
      .item-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-color);
        margin: 0;
        line-height: 1.4;
        flex: 1;
      }
      
      .item-meta {
        display: flex;
        gap: 12px;
        align-items: center;
        margin-left: 16px;
        flex-shrink: 0;
      }
    }
    
    .item-description {
      color: var(--text-color2);
      font-size: 15px;
      line-height: 1.6;
      margin-bottom: 12px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
    }
    
    .item-stats {
      display: flex;
      gap: 16px;
      font-size: 13px;
      color: var(--text-color3);
    }
  }
  
  .item-action {
    margin-left: 20px;
    flex-shrink: 0;
    
    .download-btn {
      min-width: 100px;
    }
  }
}

.download-card {
  background-color: var(--card-bg-color);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.category-tag {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  
  &.edge { background-color: #e0f2fe; color: #0277bd; }
  &.deep { background-color: #f3e5f5; color: #7b1fa2; }
  &.ai { background-color: #e8f5e8; color: #2e7d32; }
  &.robot { background-color: #fff3e0; color: #ef6c00; }
  &.other { background-color: #fce4ec; color: #c2185b; }
  &.ai { background-color: #f1f8e9; color: #558b2f; }
}

.file-size {
  font-size: 12px;
  color: var(--text-color3);
  font-weight: 500;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 12px;
  line-height: 1.4;
}

.card-description {
  color: var(--text-color2);
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 20px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  height: 4.8em;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.meta-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  
  span {
    font-size: 12px;
    color: var(--text-color3);
  }
}

.download-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
  
  &:hover {
    background-color: #2563eb;
  }
  
  .iconfont {
    font-size: 14px;
  }
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .pagination-previous,
  .pagination-next {
    padding: 8px 16px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background-color: var(--card-bg-color);
    color: var(--text-color);
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover:not(.is-disabled) {
      background-color: var(--hover-bg-color);
    }
    
    &.is-disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
  
  .pagination-list {
    display: flex;
    gap: 4px;
    list-style: none;
    margin: 0;
    padding: 0;
  }
  
  .pagination-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background-color: var(--card-bg-color);
    color: var(--text-color);
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background-color: var(--hover-bg-color);
    }
    
    &.is-current {
      background-color: #3b82f6;
      color: white;
      border-color: #3b82f6;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .controls-section {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .category-tabs {
    justify-content: center;
  }
  
  .view-toggle {
    justify-content: center;
  }
  
  .downloads-grid {
    grid-template-columns: 1fr;
  }
  
  .download-item {
    flex-direction: column;
    
    .item-header {
      flex-direction: column;
      gap: 8px;
      
      .item-meta {
        margin-left: 0;
      }
    }
    
    .item-action {
      margin-left: 0;
      margin-top: 16px;
      
      .download-btn {
        width: 100%;
        justify-content: center;
      }
    }
  }
  
  .card-footer {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .download-btn {
    justify-content: center;
  }
}
</style> 