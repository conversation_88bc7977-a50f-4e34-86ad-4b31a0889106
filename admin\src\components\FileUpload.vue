<template>
  <div class="file-upload">
    <a-upload
      :action="action"
      :file-list="fileList"
      :show-file-list="true"
      :accept="accept"
      :name="uploadName"
      :limit="limit"
      with-credentials
      @change="onChange"
      @progress="onProgress"
      @success="onSuccess"
      @error="onError"
      @remove="onRemove"
    >
      <template #upload-button>
        <a-button type="primary">
          <template #icon>
            <icon-upload />
          </template>
          选择文件
        </a-button>
      </template>

      <template #upload-item="{ fileItem }">
        <div class="arco-upload-list-item">
          <div class="arco-upload-list-item-content">
            <div class="arco-upload-list-item-name">
              <icon-file />
              <span class="arco-upload-list-item-name-text">
                {{ fileItem.name }}
              </span>
            </div>
            <div class="arco-upload-list-item-size">
              {{ formatFileSize(fileItem.file?.size || 0) }}
            </div>
          </div>

          <!-- 上传进度 -->
          <div v-if="fileItem.status === 'uploading'" class="arco-upload-list-item-progress">
            <a-progress :percent="fileItem.percent" size="small" />
          </div>

          <!-- 操作按钮 -->
          <div class="arco-upload-list-item-operation">
            <a-button
              v-if="fileItem.status === 'done' && fileItem.response?.data?.url"
              type="text"
              size="small"
              @click="previewFile(fileItem)"
            >
              <template #icon>
                <icon-eye />
              </template>
            </a-button>
            <a-button type="text" size="small" status="danger" @click="removeFile(fileItem)">
              <template #icon>
                <icon-delete />
              </template>
            </a-button>
          </div>
        </div>
      </template>
    </a-upload>

    <!-- 手动输入URL选项 -->
    <div v-if="showManualInput" class="manual-input">
      <a-divider>或</a-divider>
      <a-input v-model="manualUrl" placeholder="手动输入文件URL" @change="onManualUrlChange">
        <template #prepend>
          <icon-link />
        </template>
      </a-input>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import { getToken } from '@/utils/auth';
import type { FileItem } from '@arco-design/web-vue';

interface FileUploadProps {
  modelValue?: string;
  accept?: string;
  limit?: number;
  uploadName?: string;
  showManualInput?: boolean;
  maxSize?: number; // 最大文件大小(MB)
}

interface FileUploadEmits {
  (e: 'update:modelValue', value: string): void;
  (e: 'change', fileInfo: { url: string; name: string; size: number }): void;
}

const props = withDefaults(defineProps<FileUploadProps>(), {
  modelValue: '',
  accept: '*',
  limit: 1,
  uploadName: 'file',
  showManualInput: true,
  maxSize: 50, // 默认50MB
});

const emit = defineEmits<FileUploadEmits>();

const fileList = ref<FileItem[]>([]);
const manualUrl = ref('');

// 上传地址
const action = computed(() => {
  const baseURL = useBaseURL();
  return `${baseURL}/api/upload?userToken=${getToken()}`;
});

// 监听外部值变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue && newValue !== manualUrl.value) {
      manualUrl.value = newValue;
      // 如果有外部传入的URL，创建一个虚拟的文件项
      if (newValue && fileList.value.length === 0) {
        const fileName = newValue.split('/').pop() || 'unknown';
        fileList.value = [
          {
            uid: Date.now().toString(),
            name: fileName,
            status: 'done',
            url: newValue,
            response: { data: { url: newValue } },
          },
        ];
      }
    }
  },
  { immediate: true }
);

// 文件大小格式化
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
};

// 文件上传事件处理
const onChange = (fileItemList: FileItem[], fileItem: FileItem) => {
  // 文件大小检查
  if (fileItem.file && fileItem.file.size > props.maxSize * 1024 * 1024) {
    Message.error(`文件大小不能超过 ${props.maxSize}MB`);
    return false;
  }

  fileList.value = fileItemList;
  return true;
};

const onProgress = (fileItem: FileItem) => {
  const index = fileList.value.findIndex((item) => item.uid === fileItem.uid);
  if (index !== -1) {
    fileList.value[index] = { ...fileItem };
  }
};

const onSuccess = (fileItem: FileItem) => {
  const { response } = fileItem;
  if (response?.success && response?.data?.url) {
    const { url } = response.data;
    const fileInfo = {
      url,
      name: fileItem.name || '',
      size: fileItem.file?.size || 0,
    };

    emit('update:modelValue', url);
    emit('change', fileInfo);
    manualUrl.value = url;

    Message.success('文件上传成功');
  } else {
    Message.error('文件上传失败');
  }
};

const onError = (fileItem: FileItem) => {
  Message.error(`文件 ${fileItem.name} 上传失败`);
};

const onRemove = (fileItem: FileItem) => {
  return removeFile(fileItem);
};

const removeFile = (fileItem: FileItem): boolean => {
  const index = fileList.value.findIndex((item) => item.uid === fileItem.uid);
  if (index !== -1) {
    fileList.value.splice(index, 1);
    if (fileList.value.length === 0) {
      emit('update:modelValue', '');
      emit('change', { url: '', name: '', size: 0 });
      manualUrl.value = '';
    }
    return true;
  }
  return false;
};

const previewFile = (fileItem: FileItem) => {
  const url = fileItem.response?.data?.url || fileItem.url;
  if (url) {
    window.open(url, '_blank');
  }
};

const onManualUrlChange = () => {
  if (manualUrl.value) {
    // 清除已上传的文件
    fileList.value = [];

    // 创建虚拟文件项
    const fileName = manualUrl.value.split('/').pop() || 'manual-file';
    fileList.value = [
      {
        uid: Date.now().toString(),
        name: fileName,
        status: 'done',
        url: manualUrl.value,
        response: { data: { url: manualUrl.value } },
      },
    ];

    emit('update:modelValue', manualUrl.value);
    emit('change', {
      url: manualUrl.value,
      name: fileName,
      size: 0,
    });
  } else {
    fileList.value = [];
    emit('update:modelValue', '');
    emit('change', { url: '', name: '', size: 0 });
  }
};
</script>

<style lang="scss" scoped>
.file-upload {
  .manual-input {
    margin-top: 16px;
  }

  :deep(.arco-upload-list-item) {
    padding: 8px 12px;
    border: 1px solid var(--color-border-2);
    border-radius: 4px;
    margin-bottom: 8px;

    .arco-upload-list-item-content {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .arco-upload-list-item-name {
        display: flex;
        align-items: center;
        flex: 1;

        .arco-upload-list-item-name-text {
          margin-left: 8px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .arco-upload-list-item-size {
        color: var(--color-text-3);
        font-size: 12px;
        margin: 0 8px;
      }
    }

    .arco-upload-list-item-progress {
      margin-top: 8px;
    }

    .arco-upload-list-item-operation {
      display: flex;
      gap: 4px;
    }
  }
}
</style>
