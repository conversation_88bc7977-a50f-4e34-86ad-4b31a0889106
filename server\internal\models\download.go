package models

import (
	"time"
)

// Download 下载资料
type Download struct {
	Model
	Title         string    `gorm:"size:128;not null" json:"title" form:"title"`                  // 资料标题
	Description   string    `gorm:"type:text" json:"description" form:"description"`              // 资料描述
	Category      string    `gorm:"size:32;not null" json:"category" form:"category"`             // 资料分类
	FileUrl       string    `gorm:"type:text;not null" json:"fileUrl" form:"fileUrl"`             // 文件URL
	FileSize      int64     `gorm:"not null;default:0" json:"fileSize" form:"fileSize"`           // 文件大小(字节)
	DownloadCount int64     `gorm:"not null;default:0" json:"downloadCount" form:"downloadCount"` // 下载次数
	Status        int       `gorm:"not null;default:0" json:"status" form:"status"`               // 状态：0-正常，1-禁用
	CreateTime    time.Time `gorm:"not null" json:"createTime" form:"createTime"`                 // 创建时间
	UpdateTime    time.Time `gorm:"not null" json:"updateTime" form:"updateTime"`                 // 更新时间
}

// DownloadLog 下载记录
type DownloadLog struct {
	Model
	UserId     int64     `gorm:"not null;index:idx_download_user" json:"userId" form:"userId"`       // 用户ID
	DownloadId int64     `gorm:"not null;index:idx_download_id" json:"downloadId" form:"downloadId"` // 资料ID
	Ip         string    `gorm:"size:128" json:"ip" form:"ip"`                                       // 用户IP
	UserAgent  string    `gorm:"type:text" json:"userAgent" form:"userAgent"`                        // 用户代理
	CreateTime time.Time `gorm:"not null" json:"createTime" form:"createTime"`                       // 创建时间
}
