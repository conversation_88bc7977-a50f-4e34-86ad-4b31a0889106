import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const DOWNLOAD: AppRouteRecordRaw = {
  path: '/download',
  name: 'download',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: '资料下载',
    requiresAuth: true,
    icon: 'icon-file-pdf',
    order: 8,
  },
  children: [
    {
      path: 'list',
      name: 'DownloadList',
      component: () => import('@/views/pages/download/index.vue'),
      meta: {
        locale: '资料管理',
        requiresAuth: true,
        roles: ['*'],
      },
    },
    {
      path: 'stats',
      name: 'DownloadStats',
      component: () => import('@/views/pages/download/stats.vue'),
      meta: {
        locale: '下载统计',
        requiresAuth: true,
        roles: ['*'],
      },
    },
  ],
};

export default DOWNLOAD; 