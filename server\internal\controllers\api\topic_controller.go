package api

import (
	"bbs-go/internal/models/constants"
	"bbs-go/internal/pkg/common"
	"bbs-go/internal/pkg/errs"
	"bbs-go/internal/pkg/markdown"
	"bbs-go/internal/spam"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/mlogclub/simple/common/strs"
	"github.com/mlogclub/simple/sqls"
	"github.com/mlogclub/simple/web"
	"github.com/mlogclub/simple/web/params"

	"bbs-go/internal/cache"
	"bbs-go/internal/controllers/render"
	"bbs-go/internal/models"
	"bbs-go/internal/services"
)

type TopicController struct {
	Ctx iris.Context
}

func (c *TopicController) GetNode_navs() *web.JsonResult {
	nodes := []models.NodeResponse{
		{
			Id:   0,
			Name: "最新",
		},
		{
			Id:   -1,
			Name: "推荐",
		},
		{
			Id:   -2,
			Name: "关注",
		},
	}
	realNodes := render.BuildNodes(services.TopicNodeService.GetNodes())
	nodes = append(nodes, realNodes...)
	return web.JsonData(nodes)
}

// 节点
func (c *TopicController) GetNodes() *web.JsonResult {
	nodes := render.BuildNodes(services.TopicNodeService.GetNodes())
	return web.JsonData(nodes)
}

// 节点信息
func (c *TopicController) GetNode() *web.JsonResult {
	nodeId := params.FormValueInt64Default(c.Ctx, "nodeId", 0)
	node := services.TopicNodeService.Get(nodeId)
	return web.JsonData(render.BuildNode(node))
}

// 发表帖子
func (c *TopicController) PostCreate() *web.JsonResult {
	user := services.UserTokenService.GetCurrent(c.Ctx)
	if err := services.UserService.CheckPostStatus(user); err != nil {
		return web.JsonError(err)
	}

	form := models.GetCreateTopicForm(c.Ctx)
	if err := spam.CheckTopic(user, form); err != nil {
		return web.JsonError(err)
	}

	topic, err := services.TopicPublishService.Publish(user.Id, form)
	if err != nil {
		return web.JsonError(err)
	}
	return web.JsonData(render.BuildSimpleTopic(topic))
}

// 编辑时获取详情
func (c *TopicController) GetEditBy(topicId int64) *web.JsonResult {
	user := services.UserTokenService.GetCurrent(c.Ctx)
	if err := services.UserService.CheckPostStatus(user); err != nil {
		return web.JsonError(err)
	}

	topic := services.TopicService.Get(topicId)
	if topic == nil || topic.Status != constants.StatusOk {
		return web.JsonErrorMsg("话题不存在或已被删除")
	}
	if topic.Type != constants.TopicTypeTopic {
		return web.JsonErrorMsg("当前类型帖子不支持修改")
	}

	// 非作者、且非管理员
	if topic.UserId != user.Id && !user.HasAnyRole(constants.RoleAdmin, constants.RoleOwner) {
		return web.JsonErrorMsg("无权限")
	}

	tags := services.TopicService.GetTopicTags(topicId)
	var tagNames []string
	if len(tags) > 0 {
		for _, tag := range tags {
			tagNames = append(tagNames, tag.Name)
		}
	}

	return web.NewEmptyRspBuilder().
		Put("id", topic.Id).
		Put("nodeId", topic.NodeId).
		Put("title", topic.Title).
		Put("content", topic.Content).
		Put("hideContent", topic.HideContent).
		Put("tags", tagNames).
		JsonResult()
}

// 编辑帖子
func (c *TopicController) PostEditBy(topicId int64) *web.JsonResult {
	user := services.UserTokenService.GetCurrent(c.Ctx)
	if err := services.UserService.CheckPostStatus(user); err != nil {
		return web.JsonError(err)
	}

	topic := services.TopicService.Get(topicId)
	if topic == nil || topic.Status != constants.StatusOk {
		return web.JsonErrorMsg("话题不存在或已被删除")
	}

	// 非作者、且非管理员
	if topic.UserId != user.Id && !user.HasAnyRole(constants.RoleAdmin, constants.RoleOwner) {
		return web.JsonErrorMsg("无权限")
	}

	var (
		nodeId      = params.FormValueInt64Default(c.Ctx, "nodeId", 0)
		title       = strings.TrimSpace(params.FormValue(c.Ctx, "title"))
		content     = strings.TrimSpace(params.FormValue(c.Ctx, "content"))
		hideContent = strings.TrimSpace(params.FormValue(c.Ctx, "hideContent"))
		tags        = params.FormValueStringArray(c.Ctx, "tags")
	)

	err := services.TopicService.Edit(topicId, nodeId, tags, title, content, hideContent)
	if err != nil {
		return web.JsonError(err)
	}
	// 操作日志
	services.OperateLogService.AddOperateLog(user.Id, constants.OpTypeUpdate, constants.EntityTopic, topicId,
		"", c.Ctx.Request())
	return web.JsonData(render.BuildSimpleTopic(topic))
}

// 删除帖子
func (c *TopicController) PostDeleteBy(topicId int64) *web.JsonResult {
	user := services.UserTokenService.GetCurrent(c.Ctx)
	if err := services.UserService.CheckPostStatus(user); err != nil {
		return web.JsonError(err)
	}

	topic := services.TopicService.Get(topicId)
	if topic == nil || topic.Status != constants.StatusOk {
		return web.JsonSuccess()
	}

	// 非作者、且非管理员
	if topic.UserId != user.Id && !user.HasAnyRole(constants.RoleAdmin, constants.RoleOwner) {
		return web.JsonErrorMsg("无权限")
	}

	if err := services.TopicService.Delete(topicId, user.Id, c.Ctx.Request()); err != nil {
		return web.JsonError(err)
	}
	return web.JsonSuccess()
}

// PostRecommendBy 设为推荐
func (c *TopicController) PostRecommendBy(topicId int64) *web.JsonResult {
	recommend, err := params.FormValueBool(c.Ctx, "recommend")
	if err != nil {
		return web.JsonError(err)
	}
	user := services.UserTokenService.GetCurrent(c.Ctx)
	if user == nil {
		return web.JsonError(errs.NotLogin)
	}
	if !user.HasAnyRole(constants.RoleOwner, constants.RoleAdmin) {
		return web.JsonErrorMsg("无权限")
	}

	err = services.TopicService.SetRecommend(topicId, recommend)
	if err != nil {
		return web.JsonError(err)
	}
	return web.JsonSuccess()
}

// 帖子详情
func (c *TopicController) GetBy(topicId int64) *web.JsonResult {
	topic := services.TopicService.Get(topicId)
	if topic == nil || topic.Status == constants.StatusDeleted {
		return web.JsonErrorMsg("帖子不存在")
	}

	// 审核中文章控制展示
	user := services.UserTokenService.GetCurrent(c.Ctx)
	if topic.Status == constants.StatusReview {
		if user != nil {
			if topic.UserId != user.Id && !user.IsOwnerOrAdmin() {
				return web.JsonErrorCode(403, "文章审核中")
			}
		} else {
			return web.JsonErrorCode(403, "文章审核中")
		}
	}

	services.TopicService.IncrViewCount(topicId) // 增加浏览量
	return web.JsonData(render.BuildTopic(topic, user))
}

// 点赞用户
func (c *TopicController) GetRecentlikesBy(topicId int64) *web.JsonResult {
	likes := services.UserLikeService.Recent(constants.EntityTopic, topicId, 5)
	var users []models.UserInfo
	for _, like := range likes {
		userInfo := render.BuildUserInfoDefaultIfNull(like.UserId)
		if userInfo != nil {
			users = append(users, *userInfo)
		}
	}
	return web.JsonData(users)
}

// 最新帖子
func (c *TopicController) GetRecent() *web.JsonResult {
	user := services.UserTokenService.GetCurrent(c.Ctx)
	topics := services.TopicService.Find(sqls.NewCnd().Where("status = ?", constants.StatusOk).Desc("id").Limit(10))
	return web.JsonData(render.BuildSimpleTopics(topics, user))
}

// 用户帖子列表
func (c *TopicController) GetUserTopics() *web.JsonResult {
	userId, err := params.FormValueInt64(c.Ctx, "userId")
	if err != nil {
		return web.JsonError(err)
	}
	cursor := params.FormValueInt64Default(c.Ctx, "cursor", 0)
	user := services.UserTokenService.GetCurrent(c.Ctx)
	topics, cursor, hasMore := services.TopicService.GetUserTopics(userId, cursor)
	return web.JsonCursorData(render.BuildSimpleTopics(topics, user), strconv.FormatInt(cursor, 10), hasMore)
}

// 帖子列表
func (c *TopicController) GetTopics() *web.JsonResult {
	var (
		cursor = params.FormValueInt64Default(c.Ctx, "cursor", 0)
		nodeId = params.FormValueInt64Default(c.Ctx, "nodeId", 0)
		user   = services.UserTokenService.GetCurrent(c.Ctx)
	)
	if nodeId == constants.NodeIdFollow && user == nil {
		return web.JsonError(errs.NotLogin)
	}

	var temp []models.Topic
	if cursor <= 0 {
		stickyTopics := services.TopicService.GetStickyTopics(nodeId, 3)
		temp = append(temp, stickyTopics...)
	}
	topics, cursor, hasMore := services.TopicService.GetTopics(user, nodeId, cursor)
	for _, topic := range topics {
		topic.Sticky = false // 正常列表不要渲染置顶
		temp = append(temp, topic)
	}
	list := common.Distinct(temp, func(t models.Topic) any {
		return t.Id
	})
	return web.JsonCursorData(render.BuildSimpleTopics(list, user), strconv.FormatInt(cursor, 10), hasMore)
}

// 标签帖子列表
func (c *TopicController) GetTagTopics() *web.JsonResult {
	var (
		cursor     = params.FormValueInt64Default(c.Ctx, "cursor", 0)
		tagId, err = params.FormValueInt64(c.Ctx, "tagId")
		user       = services.UserTokenService.GetCurrent(c.Ctx)
	)
	if err != nil {
		return web.JsonError(err)
	}
	topics, cursor, hasMore := services.TopicService.GetTagTopics(tagId, cursor)
	return web.JsonCursorData(render.BuildSimpleTopics(topics, user), strconv.FormatInt(cursor, 10), hasMore)
}

// 收藏
func (c *TopicController) GetFavoriteBy(topicId int64) *web.JsonResult {
	user := services.UserTokenService.GetCurrent(c.Ctx)
	if user == nil {
		return web.JsonError(errs.NotLogin)
	}
	err := services.FavoriteService.AddTopicFavorite(user.Id, topicId)
	if err != nil {
		return web.JsonError(err)
	}
	return web.JsonSuccess()
}

// 推荐话题列表（目前逻辑为取最近50条数据随机展示）
func (c *TopicController) GetRecommend() *web.JsonResult {
	topics := cache.TopicCache.GetRecommendTopics()
	if len(topics) == 0 {
		return web.JsonSuccess()
	} else {
		dest := make([]models.Topic, len(topics))
		perm := rand.Perm(len(topics))
		for i, v := range perm {
			dest[v] = topics[i]
		}
		end := 10
		if end > len(topics) {
			end = len(topics)
		}
		ret := dest[0:end]
		return web.JsonData(render.BuildSimpleTopics(ret, nil))
	}
}

// 最新话题
func (c *TopicController) GetNewest() *web.JsonResult {
	topics := services.TopicService.Find(sqls.NewCnd().Eq("status", constants.StatusOk).Desc("id").Limit(6))
	return web.JsonData(render.BuildSimpleTopics(topics, nil))
}

// 设置置顶
func (c *TopicController) PostStickyBy(topicId int64) *web.JsonResult {
	user := services.UserTokenService.GetCurrent(c.Ctx)
	if user == nil {
		return web.JsonError(errs.NotLogin)
	}
	if !user.HasAnyRole(constants.RoleOwner, constants.RoleAdmin) {
		return web.JsonErrorMsg("无权限")
	}

	var (
		sticky = params.FormValueBoolDefault(c.Ctx, "sticky", false) // 是否指定
	)
	if err := services.TopicService.SetSticky(topicId, sticky); err != nil {
		return web.JsonError(err)
	}
	return web.JsonSuccess()
}

func (c *TopicController) GetHide_content() *web.JsonResult {
	topicId := params.FormValueInt64Default(c.Ctx, "topicId", 0)
	var (
		exists      = false // 是否有隐藏内容
		show        = false // 是否显示隐藏内容
		hideContent = ""    // 隐藏内容
	)
	topic := services.TopicService.Get(topicId)
	if topic != nil && topic.Status == constants.StatusOk && strs.IsNotBlank(topic.HideContent) {
		exists = true
		if user := services.UserTokenService.GetCurrent(c.Ctx); user != nil {
			if user.Id == topic.UserId || services.CommentService.IsCommented(user.Id, constants.EntityTopic, topic.Id) {
				show = true
				hideContent = markdown.ToHTML(topic.HideContent)
			}
		}
	}
	return web.JsonData(map[string]interface{}{
		"exists":  exists,
		"show":    show,
		"content": hideContent,
	})
}

// GetDaily_question 获取每日一问
func (c *TopicController) GetDaily_question() *web.JsonResult {
	// 从有回答的帖子中随机选择一个作为每日一问
	// 条件：帖子类型 + 正常状态 + 有评论回答
	cnd := sqls.NewCnd().
		Eq("type", constants.TopicTypeTopic). // 帖子类型
		Eq("status", constants.StatusOk).     // 正常状态
		Gt("comment_count", 0).               // 有回答
		Desc("id").                           // 按ID降序，获取较新的帖子
		Limit(50)                             // 获取前50条

	topics := services.TopicService.Find(cnd)
	if len(topics) > 0 {
		// 使用 Go 的随机数生成器从结果中随机选择一个
		randomIndex := rand.Intn(len(topics))
		topic := &topics[randomIndex]

		// 添加每日一问的特殊标识和日期
		result := map[string]interface{}{
			"id":          topic.Id,
			"title":       topic.Title,
			"date":        time.Now().Format("2006-01-02"),
			"userId":      topic.UserId,
			"username":    "", // 会在render.BuildSimpleTopic中填充
			"answerCount": topic.CommentCount,
			"likeCount":   topic.LikeCount,
			"viewCount":   topic.ViewCount,
			"status":      1, // 问答状态：0-待回答，1-有回答，2-已解决
			"createTime":  topic.CreateTime,
		}

		// 获取用户信息
		if topicUser := cache.UserCache.Get(topic.UserId); topicUser != nil {
			result["username"] = topicUser.Nickname
		}

		return web.JsonData(result)
	}

	// 如果没有找到合适的问题，返回空数据
	return web.JsonData(nil)
}

// GetHot_answers 获取热门回答列表
func (c *TopicController) GetHot_answers() *web.JsonResult {
	// 获取限制条数参数，默认10条
	limit := params.FormValueIntDefault(c.Ctx, "limit", 10)
	if limit > 50 {
		limit = 50 // 最多50条
	}
	if limit < 1 {
		limit = 10 // 最少1条
	}

	// 按回答次数从多到少显示问题，取前N条
	// 排序规则：优先按answer_count降序，再按like_count降序，最后按view_count降序
	cnd := sqls.NewCnd().
		Eq("type", constants.TopicTypeTopic). // 帖子类型
		Eq("status", constants.StatusOk).     // 正常状态
		Gt("comment_count", 0).               // 有回答
		Desc("comment_count").                // 按回答数降序
		Desc("like_count").                   // 按点赞数降序
		Desc("view_count").                   // 按浏览数降序
		Limit(limit)                          // 限制条数

	topics := services.TopicService.Find(cnd)
	if len(topics) == 0 {
		return web.JsonData([]interface{}{})
	}

	// 构建热门回答响应数据
	var result []map[string]interface{}
	for _, topic := range topics {
		// 计算热度分数 (可以根据需要调整权重)
		hotScore := topic.CommentCount*10 + topic.LikeCount*5 + topic.ViewCount

		item := map[string]interface{}{
			"id":                  topic.Id,
			"title":               topic.Title,
			"hotScore":            hotScore,
			"userId":              topic.UserId,
			"username":            "",
			"answerCount":         topic.CommentCount,
			"likeCount":           topic.LikeCount,
			"viewCount":           topic.ViewCount,
			"status":              2,  // 问答状态：0-待回答，1-回答中，2-已解决
			"latestAnswerSummary": "", // 最新回答摘要，暂时为空
			"latestAnswerTime":    topic.LastCommentTime,
			"createTime":          topic.CreateTime,
		}

		// 获取用户信息
		if topicUser := cache.UserCache.Get(topic.UserId); topicUser != nil {
			item["username"] = topicUser.Nickname
		}

		// 获取最新回答的摘要（可选实现）
		if topic.LastCommentTime > 0 {
			// 这里可以查询最新的评论作为摘要，暂时使用帖子标题作为摘要
			if len(topic.Title) > 50 {
				item["latestAnswerSummary"] = topic.Title[:50] + "..."
			} else {
				item["latestAnswerSummary"] = "该问题有 " + strconv.FormatInt(topic.CommentCount, 10) + " 个回答"
			}
		}

		result = append(result, item)
	}

	return web.JsonData(result)
}

// GetFeatured_qa 获取精选问答列表
func (c *TopicController) GetFeatured_qa() *web.JsonResult {
	// 获取限制条数参数，默认5条
	limit := params.FormValueIntDefault(c.Ctx, "limit", 5)
	if limit > 20 {
		limit = 20 // 最多20条
	}
	if limit < 1 {
		limit = 5 // 最少1条
	}

	// 精选问答：使用推荐标识 + 帖子类型来表示精选问答
	// 排序规则：优先按推荐时间降序，再按创建时间降序
	cnd := sqls.NewCnd().
		Eq("type", constants.TopicTypeTopic). // 帖子类型
		Eq("status", constants.StatusOk).     // 正常状态
		Eq("recommend", true).                // 推荐的帖子作为精选问答
		Desc("recommend_time").               // 按推荐时间降序
		Desc("create_time").                  // 按创建时间降序
		Limit(limit)                          // 限制条数

	topics := services.TopicService.Find(cnd)
	if len(topics) == 0 {
		return web.JsonData([]interface{}{})
	}

	// 构建精选问答响应数据
	var result []map[string]interface{}
	for _, topic := range topics {
		item := map[string]interface{}{
			"id":           topic.Id,
			"title":        topic.Title,
			"userId":       topic.UserId,
			"username":     "",
			"likeCount":    topic.LikeCount,
			"answerCount":  topic.CommentCount,
			"viewCount":    topic.ViewCount,
			"status":       1, // 问答状态：0-待回答，1-已回答，2-精选
			"isFeatured":   true,
			"createTime":   topic.CreateTime,
			"featuredTime": topic.RecommendTime, // 使用推荐时间作为精选时间
		}

		// 获取用户信息
		if topicUser := cache.UserCache.Get(topic.UserId); topicUser != nil {
			item["username"] = topicUser.Nickname
		}

		// 添加标签信息（如果需要的话）
		if topic.CommentCount > 0 {
			item["status"] = 2 // 有回答且是精选的
		}

		result = append(result, item)
	}

	return web.JsonData(result)
}

// GetWeekly_adoption_rank 获取七天问答采用榜
func (c *TopicController) GetWeekly_adoption_rank() *web.JsonResult {
	// 获取限制条数参数，默认10条
	limit := params.FormValueIntDefault(c.Ctx, "limit", 10)
	if limit > 50 {
		limit = 50 // 最多50条
	}
	if limit < 1 {
		limit = 10 // 最少1条
	}

	// 计算7天前的时间戳（毫秒）
	sevenDaysAgo := time.Now().AddDate(0, 0, -7).Unix() * 1000

	// 查询7天内的评论，按用户分组统计被点赞最多的回答
	// 使用原生SQL查询以实现复杂的统计逻辑
	sqlStr := `
		SELECT 
			c.user_id,
			u.nickname as username,
			u.avatar as avatar,
			COUNT(c.id) as answer_count,
			SUM(COALESCE(ul.like_count, 0)) as total_likes,
			SUM(COALESCE(ul.like_count, 0)) as adoption_count
		FROM t_comment c
		LEFT JOIN t_user u ON c.user_id = u.id
		LEFT JOIN (
			SELECT entity_id, COUNT(*) as like_count 
			FROM t_user_like 
			WHERE entity_type = 'comment' 
			GROUP BY entity_id
		) ul ON c.id = ul.entity_id
		WHERE c.entity_type = 'topic' 
			AND c.status = 0 
			AND c.create_time >= ?
			AND u.status = 0
		GROUP BY c.user_id, u.nickname, u.avatar
		HAVING answer_count > 0
		ORDER BY adoption_count DESC, answer_count DESC, c.user_id ASC
		LIMIT ?
	`

	type RankResult struct {
		UserId        int64  `json:"userId"`
		Username      string `json:"username"`
		Avatar        string `json:"avatar"`
		AnswerCount   int64  `json:"answerCount"`
		TotalLikes    int64  `json:"totalLikes"`
		AdoptionCount int64  `json:"adoptionCount"`
	}

	var rankResults []RankResult
	err := sqls.DB().Raw(sqlStr, sevenDaysAgo, limit).Scan(&rankResults).Error
	if err != nil {
		return web.JsonError(err)
	}

	// 构建七天问答采用榜响应数据
	var result []map[string]interface{}
	for i, rank := range rankResults {
		item := map[string]interface{}{
			"rank":          i + 1,
			"userId":        rank.UserId,
			"username":      rank.Username,
			"avatar":        rank.Avatar,
			"adoptionCount": rank.AdoptionCount, // 被采用次数（这里用总点赞数代替）
			"answerCount":   rank.AnswerCount,   // 回答总数
			"totalLikes":    rank.TotalLikes,    // 总获赞数
			"weekPeriod":    time.Now().AddDate(0, 0, -7).Format("2006-01-02") + " ~ " + time.Now().Format("2006-01-02"),
		}

		// 根据排名设置特殊样式标识
		if i < 3 {
			item["topThree"] = true
		}

		result = append(result, item)
	}

	// 如果没有数据，返回空数组
	if len(result) == 0 {
		return web.JsonData([]interface{}{})
	}

	return web.JsonData(result)
}
