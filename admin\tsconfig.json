{"compilerOptions": {"target": "ES2020", "module": "ES2020", "moduleResolution": "node", "strict": true, "noImplicitAny": false, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "baseUrl": ".", "outDir": "./dist", "paths": {"@/*": ["src/*"]}, "lib": ["es2020", "dom"], "skipLibCheck": true, "allowJs": true}, "include": ["src/**/*", "src/**/*.vue"], "exclude": ["node_modules"]}