package repositories

import (
	"bbs-go/internal/models"

	"github.com/mlogclub/simple/sqls"
	"github.com/mlogclub/simple/web/params"
	"gorm.io/gorm"
)

var DownloadLogRepository = newDownloadLogRepository()

func newDownloadLogRepository() *downloadLogRepository {
	return &downloadLogRepository{}
}

type downloadLogRepository struct {
}

func (r *downloadLogRepository) Get(db *gorm.DB, id int64) *models.DownloadLog {
	ret := &models.DownloadLog{}
	if err := db.First(ret, "id = ?", id).Error; err != nil {
		return nil
	}
	return ret
}

func (r *downloadLogRepository) Take(db *gorm.DB, where ...interface{}) *models.DownloadLog {
	ret := &models.DownloadLog{}
	if err := db.Take(ret, where...).Error; err != nil {
		return nil
	}
	return ret
}

func (r *downloadLogRepository) Find(db *gorm.DB, cnd *sqls.Cnd) (list []models.DownloadLog) {
	cnd.Find(db, &list)
	return
}

func (r *downloadLogRepository) FindOne(db *gorm.DB, cnd *sqls.Cnd) *models.DownloadLog {
	ret := &models.DownloadLog{}
	if err := cnd.FindOne(db, ret); err != nil {
		return nil
	}
	return ret
}

func (r *downloadLogRepository) FindPageByParams(db *gorm.DB, params *params.QueryParams) (list []models.DownloadLog, paging *sqls.Paging) {
	return r.FindPageByCnd(db, &params.Cnd)
}

func (r *downloadLogRepository) FindPageByCnd(db *gorm.DB, cnd *sqls.Cnd) (list []models.DownloadLog, paging *sqls.Paging) {
	cnd.Find(db, &list)
	count := cnd.Count(db, &models.DownloadLog{})
	paging = &sqls.Paging{
		Page:  cnd.Paging.Page,
		Limit: cnd.Paging.Limit,
		Total: count,
	}
	return
}

func (r *downloadLogRepository) Create(db *gorm.DB, t *models.DownloadLog) (err error) {
	err = db.Create(t).Error
	return
}

func (r *downloadLogRepository) Count(db *gorm.DB, cnd *sqls.Cnd) int64 {
	return cnd.Count(db, &models.DownloadLog{})
}
