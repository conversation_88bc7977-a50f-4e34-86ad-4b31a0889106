# BBS-GO 本地文件上传配置说明

## 📋 概述

BBS-GO 支持多种文件上传方式，包括阿里云OSS、腾讯云COS和本地上传。本文档主要介绍如何配置本地文件上传。

## 🔧 实现架构

### 上传流程
1. **前端上传** → `POST /api/upload`
2. **后端处理** → `UploadController.Post()`
3. **服务调用** → `UploadService.PutImage()`
4. **本地存储** → `LocalUploader.PutObject()`
5. **返回URL** → 文件访问地址

### 文件存储结构
```
uploads/
├── images/
│   ├── 2025/01/02/
│   │   ├── abc123def456.jpg
│   │   ├── def456ghi789.png
│   │   └── ...
│   └── ...
└── test/
    └── images/  # 开发环境
```

## ⚙️ 配置方法

### 1. 修改配置文件

编辑 `bbs-go.yaml` 配置文件：

```yaml
# 上传配置
Uploader:
  # 启用本地上传
  Enable: Local
  
  # 本地文件上传配置
  Local:
    Host: http://localhost:8082/  # 文件访问域名
    Path: ./uploads               # 上传目录
```

### 2. 配置参数说明

| 参数 | 说明 | 示例 |
|------|------|------|
| `Enable` | 启用的上传方式 | `Local` |
| `Local.Host` | 文件访问域名 | `http://localhost:8082/` |
| `Local.Path` | 上传目录路径 | `./uploads` |

### 3. 配置示例

#### 开发环境
```yaml
Uploader:
  Enable: Local
  Local:
    Host: http://localhost:8082/
    Path: ./uploads
```

#### 生产环境
```yaml
Uploader:
  Enable: Local
  Local:
    Host: https://yourdomain.com/
    Path: /data/uploads
```

## 🚀 部署说明

### 1. 目录权限

确保上传目录有正确的权限：

```bash
# 创建上传目录
mkdir -p ./uploads

# 设置权限（Linux/Mac）
chmod 755 ./uploads
```

### 2. 静态文件服务

程序已自动配置静态文件服务：
- 路由：`/uploads/*`
- 目录：`./uploads`
- 功能：压缩、缓存

### 3. 文件访问

上传成功后，文件可通过以下URL访问：
```
http://localhost:8082/uploads/images/2025/01/02/abc123def456.jpg
```

## 🔒 安全考虑

### 1. 文件类型限制

目前支持的文件类型：
- 图片：jpg, jpeg, png, gif, webp
- 最大大小：10MB

### 2. 文件名安全

- 使用MD5哈希作为文件名，避免冲突
- 按日期分目录存储，便于管理
- 自动添加正确的文件扩展名

### 3. 目录遍历防护

- 禁止目录列表显示
- 严格验证文件路径
- 自动创建必要目录

## 🛠️ 故障排除

### 1. 上传失败

**问题**：上传时返回错误
**解决**：
1. 检查目录权限：`ls -la uploads/`
2. 检查磁盘空间：`df -h`
3. 检查配置：确认 `Enable: Local`

### 2. 文件无法访问

**问题**：上传成功但无法访问
**解决**：
1. 检查Host配置是否正确
2. 确认静态文件服务正常
3. 检查防火墙设置

### 3. 路径问题

**问题**：文件保存到错误位置
**解决**：
1. 使用绝对路径：`Path: /data/uploads`
2. 检查程序运行目录
3. 确认目录创建成功

## 📁 文件管理

### 1. 清理策略

定期清理无用文件：
```bash
# 查找30天前的文件
find ./uploads -type f -mtime +30

# 删除30天前的文件（谨慎操作）
find ./uploads -type f -mtime +30 -delete
```

### 2. 备份建议

```bash
# 定期备份上传目录
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz uploads/
```

### 3. 监控存储

```bash
# 查看目录大小
du -sh uploads/

# 查看文件数量
find uploads/ -type f | wc -l
```

## 🔄 迁移指南

### 从云存储迁移到本地

1. 修改配置文件
2. 下载现有文件到本地
3. 更新数据库中的文件URL
4. 重启服务

### 从本地迁移到云存储

1. 上传本地文件到云存储
2. 修改配置文件
3. 更新数据库中的文件URL
4. 重启服务

## 📚 开发指南

### 1. 添加新的上传器

1. 实现 `Uploader` 接口
2. 在 `UploadService` 中注册
3. 添加配置结构体
4. 更新配置文件

### 2. 自定义文件处理

可以在 `LocalUploader` 中添加：
- 图片压缩
- 格式转换
- 水印添加
- 病毒扫描

## ❓ 常见问题

**Q: 本地上传相比云存储有什么优缺点？**
A: 
- 优点：成本低、隐私保护、响应快
- 缺点：需要备份、扩展性差、带宽消耗

**Q: 可以同时使用多种上传方式吗？**
A: 目前只支持配置一种上传方式，但可以通过代码扩展支持多种方式。

**Q: 如何限制上传文件的类型？**
A: 在 `UploadController` 中可以添加文件类型检查逻辑。 