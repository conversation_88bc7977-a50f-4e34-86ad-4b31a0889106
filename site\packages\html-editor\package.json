{"name": "html-editor", "private": true, "version": "0.0.0", "type": "module", "main": "./src/index.ts", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@tiptap/extension-image": "3.0.0-beta.5", "@tiptap/extension-list": "3.0.0-beta.5", "@tiptap/extension-placeholder": "3.0.0-beta.5", "@tiptap/extension-table": "3.0.0-beta.5", "@tiptap/extension-table-cell": "3.0.0-beta.5", "@tiptap/extension-table-header": "3.0.0-beta.5", "@tiptap/extension-table-row": "3.0.0-beta.5", "@tiptap/extension-text-align": "3.0.0-beta.5", "@tiptap/extension-text-style": "3.0.0-beta.5", "@tiptap/extension-typography": "3.0.0-beta.5", "@tiptap/pm": "3.0.0-beta.5", "@tiptap/starter-kit": "3.0.0-beta.5", "@tiptap/suggestion": "3.0.0-beta.5", "@tiptap/vue-3": "3.0.0-beta.5", "lucide-vue-next": "^0.484.0", "tippy.js": "^6.3.7", "vue": "^3.5.13"}, "devDependencies": {"@tsconfig/node20": "^20.1.5", "@types/node": "^22.13.14", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "sass": "^1.86.0", "typescript": "~5.7.2", "vite": "^6.2.0", "vue-tsc": "^2.2.4"}}