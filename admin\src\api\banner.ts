import axios from 'axios';

export interface Banner {
  id?: number;
  title: string;
  subtitle?: string;
  imageUrl: string;
  link?: string;
  target?: string;
  sort?: number;
  status?: number;
  startTime?: string;
  endTime?: string;
  clickCount?: number;
  createTime?: string;
  updateTime?: string;
}

export interface BannerQueryParams {
  page?: number;
  limit?: number;
  title?: string;
  status?: number;
}

export interface BannerDeleteParams {
  id: number;
}

export interface BannerListResponse {
  results: Banner[];
  page: {
    page: number;
    limit: number;
    total: number;
  };
}

export function queryBannerList(params: BannerQueryParams): Promise<BannerListResponse> {
  return axios.get('/api/admin/banner/list', { params });
}

export function getBanner(id: number) {
  return axios.get(`/api/admin/banner/${id}`);
}

export function createBanner(data: Banner) {
  return axios.post('/api/admin/banner', data);
}

export function updateBanner(data: Banner) {
  return axios.put('/api/admin/banner', data);
}

export function deleteBanner(params: BannerDeleteParams) {
  return axios.delete('/api/admin/banner', { params });
}

export interface BannerStatsResponse {
  totalClicks: number;
  bannerStats: Array<{
    id: number;
    title: string;
    clickCount: number;
  }>;
  promotionStats: Array<{
    id: number;
    title: string;
    clickCount: number;
  }>;
}

export function queryBannerStats(): Promise<BannerStatsResponse> {
  return axios.get('/api/admin/banner/stats');
} 