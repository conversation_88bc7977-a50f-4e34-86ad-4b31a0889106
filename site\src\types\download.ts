/**
 * 下载资料数据结构
 */
export interface DownloadItem {
  id: number;                // 资料ID
  title: string;             // 资料标题
  description: string;       // 资料描述
  category: string;          // 资料分类
  fileUrl: string;           // 文件URL
  fileSize: number;          // 文件大小(字节)
  formattedSize?: string;    // 格式化后的文件大小
  downloadCount: number;     // 下载次数
  status: number;            // 状态：0-正常，1-禁用
  createTime: string;        // 创建时间
  updateTime: string;        // 更新时间
}

/**
 * 下载资料分页响应
 */
export interface DownloadPageResponse {
  results: DownloadItem[];   // 资料列表
  page: number;              // 当前页码
  limit: number;             // 每页数量
  total: number;             // 总数量
  totalPages: number;        // 总页数
}

/**
 * 下载资料分类
 */
export interface DownloadCategory {
  key: string;               // 分类键名
  name: string;              // 分类名称
  count?: number;            // 该分类下的资料数量
}

/**
 * API响应数据结构
 */
export interface ApiResponse<T = any> {
  /** 响应代码，0表示成功 */
  code: number;
  /** 响应消息 */
  message: string;
  /** 响应数据 */
  data?: T;
} 