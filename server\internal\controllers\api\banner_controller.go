package api

import (
	"bbs-go/internal/services"

	"github.com/kataras/iris/v12"
	"github.com/mlogclub/simple/web"
)

type BannerController struct {
	Ctx iris.Context
}

// GetList 获取轮播图列表
func (c *BannerController) GetList() *web.JsonResult {
	list := services.BannerService.GetActive()
	return web.JsonData(list)
}

// GetPromotions 获取推广图片列表
func (c *BannerController) GetPromotions() *web.JsonResult {
	list := services.PromotionService.GetActive()
	return web.JsonData(list)
}

// PostClick 记录点击统计
func (c *BannerController) PostClick() *web.JsonResult {
	var params struct {
		BannerId  int64  `json:"bannerId"`
		ClickType string `json:"clickType"`
	}
	err := c.Ctx.ReadJSON(&params)
	if err != nil {
		return web.JsonErrorMsg(err.Error())
	}

	if params.BannerId <= 0 {
		return web.JsonErrorMsg("参数错误")
	}

	if params.ClickType == "banner" {
		services.BannerService.IncrClickCount(params.BannerId)
	} else if params.ClickType == "promotion" {
		services.PromotionService.IncrClickCount(params.BannerId)
	}

	return web.JsonSuccess()
}
