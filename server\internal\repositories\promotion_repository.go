package repositories

import (
	"github.com/mlogclub/simple/sqls"
	"github.com/mlogclub/simple/web/params"
	"gorm.io/gorm"

	"bbs-go/internal/models"
)

var PromotionRepository = newPromotionRepository()

func newPromotionRepository() *promotionRepository {
	return &promotionRepository{}
}

type promotionRepository struct {
}

func (r *promotionRepository) Get(db *gorm.DB, id int64) *models.Promotion {
	ret := &models.Promotion{}
	if err := db.First(ret, "id = ?", id).Error; err != nil {
		return nil
	}
	return ret
}

func (r *promotionRepository) Take(db *gorm.DB, where ...interface{}) *models.Promotion {
	ret := &models.Promotion{}
	if err := db.Take(ret, where...).Error; err != nil {
		return nil
	}
	return ret
}

func (r *promotionRepository) Find(db *gorm.DB, cnd *sqls.Cnd) (list []models.Promotion) {
	cnd.Find(db, &list)
	return
}

func (r *promotionRepository) FindOne(db *gorm.DB, cnd *sqls.Cnd) *models.Promotion {
	ret := &models.Promotion{}
	if err := cnd.FindOne(db, &ret); err != nil {
		return nil
	}
	return ret
}

func (r *promotionRepository) FindPageByParams(db *gorm.DB, params *params.QueryParams) (list []models.Promotion, paging *sqls.Paging) {
	return r.FindPageByCnd(db, &params.Cnd)
}

func (r *promotionRepository) FindPageByCnd(db *gorm.DB, cnd *sqls.Cnd) (list []models.Promotion, paging *sqls.Paging) {
	cnd.Find(db, &list)
	count := cnd.Count(db, &models.Promotion{})
	paging = &sqls.Paging{
		Page:  cnd.Paging.Page,
		Limit: cnd.Paging.Limit,
		Total: count,
	}
	return
}

func (r *promotionRepository) Create(db *gorm.DB, t *models.Promotion) (err error) {
	err = db.Create(t).Error
	return
}

func (r *promotionRepository) Update(db *gorm.DB, t *models.Promotion) (err error) {
	err = db.Save(t).Error
	return
}

func (r *promotionRepository) Updates(db *gorm.DB, id int64, columns map[string]interface{}) (err error) {
	err = db.Model(&models.Promotion{}).Where("id = ?", id).Updates(columns).Error
	return
}

func (r *promotionRepository) UpdateColumn(db *gorm.DB, id int64, name string, value interface{}) (err error) {
	err = db.Model(&models.Promotion{}).Where("id = ?", id).UpdateColumn(name, value).Error
	return
}

func (r *promotionRepository) Delete(db *gorm.DB, id int64) {
	db.Delete(&models.Promotion{}, "id = ?", id)
}

// FindActive 查询启用的推广图片
func (r *promotionRepository) FindActive(db *gorm.DB) []models.Promotion {
	return r.Find(db, sqls.NewCnd().Eq("status", 1).Desc("sort"))
}
