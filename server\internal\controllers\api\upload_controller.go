package api

import (
	"bbs-go/internal/models/constants"
	"io"
	"log/slog"
	"strconv"
	"strings"

	"github.com/kataras/iris/v12"
	"github.com/mlogclub/simple/web"

	"bbs-go/internal/services"
)

type UploadController struct {
	Ctx iris.Context
}

func (c *UploadController) Post() *web.JsonResult {
	user := services.UserTokenService.GetCurrent(c.Ctx)
	if err := services.UserService.CheckPostStatus(user); err != nil {
		return web.JsonError(err)
	}

	// 尝试获取 image 字段（兼容现有图片上传）
	file, header, err := c.Ctx.FormFile("image")
	if err != nil {
		// 如果没有 image 字段，尝试获取 file 字段（通用文件上传）
		file, header, err = c.Ctx.FormFile("file")
		if err != nil {
			return web.JsonError(err)
		}
	}
	defer file.Close()

	if header.Size > constants.UploadMaxBytes {
		return web.JsonErrorMsg("文件不能超过" + strconv.Itoa(constants.UploadMaxM) + "M")
	}

	contentType := header.Header.Get("Content-Type")
	fileBytes, err := io.ReadAll(file)
	if err != nil {
		return web.JsonError(err)
	}

	slog.Info("上传文件：", slog.Any("filename", header.Filename), slog.Any("size", header.Size))

	// 根据文件类型选择上传方式
	var url string
	if isImageFile(contentType, header.Filename) {
		// 图片文件使用原有的图片上传服务
		url, err = services.UploadService.PutImage(fileBytes, contentType)
	} else {
		// 其他文件使用通用对象上传服务
		key := generateFileKey(header.Filename)
		url, err = services.UploadService.PutObject(key, fileBytes, contentType)
	}

	if err != nil {
		return web.JsonError(err)
	}

	// 返回文件信息
	return web.NewEmptyRspBuilder().
		Put("url", url).
		Put("filename", header.Filename).
		Put("size", header.Size).
		Put("contentType", contentType).
		JsonResult()
}

// 判断是否为图片文件
func isImageFile(contentType, filename string) bool {
	// 基于 Content-Type 判断
	if strings.HasPrefix(contentType, "image/") {
		return true
	}

	// 基于文件扩展名判断
	filename = strings.ToLower(filename)
	imageExts := []string{".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"}
	for _, ext := range imageExts {
		if strings.HasSuffix(filename, ext) {
			return true
		}
	}

	return false
}

// 生成文件存储key
func generateFileKey(filename string) string {
	// 简单的文件key生成，可以根据需要扩展
	return "files/" + filename
}
