import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const BANNER: AppRouteRecordRaw = {
  path: '/banner',
  name: 'banner',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: '轮播图管理',
    requiresAuth: true,
    icon: 'icon-image',
    order: 7,
  },
  children: [
    {
      path: 'list',
      name: 'BannerList',
      component: () => import('@/views/pages/banner/index.vue'),
      meta: {
        locale: '轮播图列表',
        requiresAuth: true,
        roles: ['*'],
      },
    },
    {
      path: 'promotion',
      name: 'PromotionList',
      component: () => import('@/views/pages/banner/promotion.vue'),
      meta: {
        locale: '推广图片',
        requiresAuth: true,
        roles: ['*'],
      },
    },
    {
      path: 'stats',
      name: 'BannerStats',
      component: () => import('@/views/pages/banner/stats.vue'),
      meta: {
        locale: '点击统计',
        requiresAuth: true,
        roles: ['*'],
      },
    },
  ],
};

export default BANNER; 