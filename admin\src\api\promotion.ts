import axios from 'axios';

export interface Promotion {
  id?: number;
  title: string;
  subtitle?: string;
  imageUrl: string;
  buttonText: string;
  link?: string;
  target?: string;
  backgroundColor?: string;
  textColor?: string;
  sort?: number;
  status?: number;
  startTime?: string;
  endTime?: string;
  clickCount?: number;
  createTime?: string;
  updateTime?: string;
}

export interface PromotionQueryParams {
  page?: number;
  limit?: number;
  title?: string;
  status?: number;
}

export interface PromotionDeleteParams {
  id: number;
}

export interface PromotionListResponse {
  results: Promotion[];
  page: {
    page: number;
    limit: number;
    total: number;
  };
}

export function queryPromotionList(params: PromotionQueryParams): Promise<PromotionListResponse> {
  return axios.get('/api/admin/promotion/list', { params });
}

export function getPromotion(id: number) {
  return axios.get(`/api/admin/promotion/${id}`);
}

export function createPromotion(data: Promotion) {
  return axios.post('/api/admin/promotion', data);
}

export function updatePromotion(data: Promotion) {
  return axios.put('/api/admin/promotion', data);
}

export function deletePromotion(params: PromotionDeleteParams) {
  return axios.delete('/api/admin/promotion', { params });
} 