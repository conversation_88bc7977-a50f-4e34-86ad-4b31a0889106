<!-- 轮播图点击统计页面 -->
<template>
  <div class="container">
    <Breadcrumb :items="['轮播图管理', '点击统计']" />
    <a-card class="general-card">
      <template #title>
        点击统计
      </template>
      
      <a-spin :loading="loading" style="width: 100%">
        <a-row :gutter="16">
          <a-col :span="24">
            <a-card class="stat-card">
              <template #title>总览</template>
              <a-statistic
                title="总点击量"
                :value="statsData.totalClicks || 0"
                animation
                show-group-separator
              >
                <template #suffix>
                  <icon-click />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
        </a-row>
        
        <a-row :gutter="16" style="margin-top: 16px">
          <a-col :span="12">
            <a-card class="stat-card">
              <template #title>轮播图点击排行</template>
              <a-table
                :columns="bannerColumns"
                :data="statsData.bannerStats || []"
                :pagination="false"
                :bordered="false"
              >
                <template #clicks="{ record }">
                  <a-progress
                    :percent="getClickPercent(record.clickCount, maxBannerClicks)"
                    :show-text="false"
                    status="success"
                    style="margin-right: 10px"
                  />
                  {{ record.clickCount }}
                </template>
              </a-table>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card class="stat-card">
              <template #title>推广图片点击排行</template>
              <a-table
                :columns="promotionColumns"
                :data="statsData.promotionStats || []"
                :pagination="false"
                :bordered="false"
              >
                <template #clicks="{ record }">
                  <a-progress
                    :percent="getClickPercent(record.clickCount, maxPromotionClicks)"
                    :show-text="false"
                    status="warning"
                    style="margin-right: 10px"
                  />
                  {{ record.clickCount }}
                </template>
              </a-table>
            </a-card>
          </a-col>
        </a-row>
      </a-spin>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import { queryBannerStats } from '@/api/banner';

interface StatsItem {
  id: number;
  title: string;
  clickCount: number;
  date?: string;
}

interface StatsData {
  totalClicks: number;
  bannerStats: StatsItem[];
  promotionStats: StatsItem[];
}

const loading = ref(false);
const statsData = reactive<StatsData>({
  totalClicks: 0,
  bannerStats: [],
  promotionStats: []
});

const bannerColumns = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 80,
  },
  {
    title: '标题',
    dataIndex: 'title',
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '点击数',
    slotName: 'clicks',
  }
];

const promotionColumns = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 80,
  },
  {
    title: '标题',
    dataIndex: 'title',
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '点击数',
    slotName: 'clicks',
  }
];

const maxBannerClicks = computed(() => {
  if (!statsData.bannerStats || statsData.bannerStats.length === 0) return 0;
  return Math.max(...statsData.bannerStats.map(item => item.clickCount));
});

const maxPromotionClicks = computed(() => {
  if (!statsData.promotionStats || statsData.promotionStats.length === 0) return 0;
  return Math.max(...statsData.promotionStats.map(item => item.clickCount));
});

const getClickPercent = (clicks: number, maxClicks: number) => {
  if (!maxClicks) return 0;
  return Math.round((clicks / maxClicks) * 100);
};

onMounted(async () => {
  await fetchData();
});

const fetchData = async () => {
  try {
    loading.value = true;
    const res = await queryBannerStats();
    
    if (res) {
      statsData.totalClicks = res.totalClicks || 0;
      statsData.bannerStats = res.bannerStats || [];
      statsData.promotionStats = res.promotionStats || [];
    }
  } catch (err) {
    console.error(err);
    Message.error('获取统计数据失败');
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.container {
  padding: 0 20px 20px 20px;
}

.stat-card {
  width: 100%;
  margin-bottom: 16px;
}

:deep(.arco-table-th) {
  background-color: var(--color-neutral-2);
}

:deep(.general-card) {
  margin-top: 16px;
}
</style> 