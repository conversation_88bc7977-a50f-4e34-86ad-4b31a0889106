package admin

import (
	"time"

	"github.com/kataras/iris/v12"
	"github.com/mlogclub/simple/web"
	"github.com/mlogclub/simple/web/params"

	"bbs-go/internal/models"
	"bbs-go/internal/services"
)

type PromotionController struct {
	Ctx iris.Context
}

// GetList 获取推广图片列表
func (c *PromotionController) GetList() *web.JsonResult {
	list, paging := services.PromotionService.FindPageByParams(params.NewQueryParams(c.Ctx).
		EqByReq("status").
		LikeByReq("title").
		PageByReq().Desc("id"))
	return web.JsonData(&web.PageResult{Results: list, Page: paging})
}

// GetBy 获取推广图片详情
func (c *PromotionController) GetBy(id int64) *web.JsonResult {
	t := services.PromotionService.Get(id)
	if t == nil {
		return web.JsonErrorMsg("数据不存在")
	}
	return web.JsonData(t)
}

// Post 创建推广图片
func (c *PromotionController) Post() *web.JsonResult {
	t := &models.Promotion{}
	err := params.ReadJSON(c.Ctx, t)
	if err != nil {
		return web.JsonErrorMsg(err.Error())
	}

	t.CreateTime = time.Now()
	t.UpdateTime = time.Now()
	err = services.PromotionService.Create(t)
	if err != nil {
		return web.JsonErrorMsg(err.Error())
	}
	return web.JsonData(t)
}

// Put 更新推广图片
func (c *PromotionController) Put() *web.JsonResult {
	id, err := params.FormValueInt64(c.Ctx, "id")
	if err != nil {
		return web.JsonErrorMsg(err.Error())
	}
	t := services.PromotionService.Get(id)
	if t == nil {
		return web.JsonErrorMsg("数据不存在")
	}

	err = params.ReadForm(c.Ctx, t)
	if err != nil {
		return web.JsonErrorMsg(err.Error())
	}

	t.UpdateTime = time.Now()
	err = services.PromotionService.Update(t)
	if err != nil {
		return web.JsonErrorMsg(err.Error())
	}
	return web.JsonData(t)
}

// Delete 删除推广图片
func (c *PromotionController) Delete() *web.JsonResult {
	id, err := params.FormValueInt64(c.Ctx, "id")
	if err != nil {
		return web.JsonErrorMsg(err.Error())
	}

	services.PromotionService.Delete(id)
	return web.JsonSuccess()
}
