/**
 * 轮播图数据结构
 */
export interface BannerItem {
  /** 轮播图ID */
  id: number;
  /** 标题 */
  title: string;
  /** 副标题/描述 */
  subtitle?: string;
  /** 图片URL (前端使用) */
  image: string;
  /** 图片URL (后端返回) */
  imageUrl?: string;
  /** 点击跳转链接 */
  link?: string;
  /** 链接打开方式: '_self' | '_blank' */
  target?: '_self' | '_blank';
  /** 排序权重，数字越大越靠前 */
  sort?: number;
  /** 是否启用 */
  enabled?: boolean;
  /** 状态：0-禁用，1-启用 (后端返回) */
  status?: number;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 点击次数统计 */
  clickCount?: number;
}

/**
 * 小图片推广数据结构
 */
export interface PromotionItem {
  /** 推广图片ID */
  id: number;
  /** 标题 */
  title: string;
  /** 副标题/描述 */
  subtitle?: string;
  /** 图片URL (前端使用) */
  image: string;
  /** 图片URL (后端返回) */
  imageUrl?: string;
  /** 按钮文字 */
  buttonText: string;
  /** 点击跳转链接 */
  link?: string;
  /** 链接打开方式 */
  target?: '_self' | '_blank';
  /** 背景色（可选，用于主题定制） */
  backgroundColor?: string;
  /** 文字颜色（可选） */
  textColor?: string;
  /** 排序权重 */
  sort?: number;
  /** 是否启用 */
  enabled?: boolean;
  /** 状态：0-禁用，1-启用 (后端返回) */
  status?: number;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 点击次数统计 */
  clickCount?: number;
}

/**
 * API响应数据结构
 */
export interface ApiResponse<T = any> {
  /** 响应代码，0表示成功 */
  code: number;
  /** 响应消息 */
  message: string;
  /** 响应数据 */
  data?: T;
}

/**
 * 轮播图列表响应
 */
export interface BannerListResponse extends ApiResponse<BannerItem[]> {}

/**
 * 推广图片列表响应
 */
export interface PromotionListResponse extends ApiResponse<PromotionItem[]> {}

/**
 * 点击统计数据结构
 */
export interface ClickStatsData {
  /** 总点击数 */
  totalClicks: number;
  /** 轮播图统计 */
  bannerStats: Array<{
    id: number;
    title: string;
    clicks: number;
    date?: string;
  }>;
  /** 推广图片统计 */
  promotionStats: Array<{
    id: number;
    title: string;
    clicks: number;
    date?: string;
  }>;
}

/**
 * 点击统计响应
 */
export interface ClickStatsResponse extends ApiResponse<ClickStatsData> {}

/**
 * 轮播图配置选项
 */
export interface BannerConfig {
  /** 自动播放间隔（毫秒） */
  autoplayInterval?: number;
  /** 是否显示指示器 */
  showIndicators?: boolean;
  /** 是否显示箭头 */
  showArrows?: boolean;
  /** 是否启用悬停暂停 */
  pauseOnHover?: boolean;
  /** 过渡动画时间（毫秒） */
  transitionDuration?: number;
} 