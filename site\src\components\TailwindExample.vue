<template>
  <div class="tailwind-example">
    <!-- 使用现有Bulma样式 -->
    <div class="notification is-primary">
      <strong>现有样式系统:</strong> 这是使用 Bulma 框架的提示框
    </div>

    <!-- 使用Tailwind CSS -->
    <div class="tw-bg-theme border tw-border-theme rounded-lg p-4 mt-4">
      <h3 class="text-lg font-semibold tw-text-theme mb-2">
        Tailwind CSS 示例
      </h3>
      <p class="tw-text-theme mb-4">
        这个组件使用 Tailwind CSS 工具类，并支持主题切换
      </p>
      
      <!-- 自定义Tailwind按钮 -->
      <div class="flex gap-3">
        <button class="tw-btn-primary">
          Tailwind 主按钮
        </button>
        <button class="tw-btn-secondary">
          Tailwind 次按钮
        </button>
      </div>
    </div>

    <!-- 响应式网格布局 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
      <div class="tw-bg-theme border tw-border-theme rounded-lg p-4">
        <h4 class="font-medium tw-text-theme">卡片 1</h4>
        <p class="text-sm tw-text-theme opacity-70">响应式布局示例</p>
      </div>
      <div class="tw-bg-theme border tw-border-theme rounded-lg p-4">
        <h4 class="font-medium tw-text-theme">卡片 2</h4>
        <p class="text-sm tw-text-theme opacity-70">自适应主题颜色</p>
      </div>
      <div class="tw-bg-theme border tw-border-theme rounded-lg p-4">
        <h4 class="font-medium tw-text-theme">卡片 3</h4>
        <p class="text-sm tw-text-theme opacity-70">Tailwind 工具类</p>
      </div>
    </div>

    <!-- 混合使用示例 -->
    <div class="widget has-border mt-6">
      <div class="widget-header">
        <h3>混合样式示例</h3>
      </div>
      <div class="p-4">
        <div class="flex items-center justify-between">
          <span class="tag is-primary">Bulma 标签</span>
          <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
            Tailwind 标签
          </span>
        </div>
      </div>
    </div>

    <!-- Tailwind 工具类展示 -->
    <div class="mt-6 p-6 bg-gray-50 rounded-lg">
      <h4 class="text-lg font-semibold mb-4">Tailwind 工具类示例</h4>
      <div class="space-y-3">
        <div class="flex items-center gap-4">
          <div class="w-4 h-4 bg-red-500 rounded"></div>
          <span class="text-sm">颜色工具类</span>
        </div>
        <div class="flex items-center gap-4">
          <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"></div>
          <span class="text-sm">渐变背景</span>
        </div>
        <div class="flex items-center gap-4">
          <div class="w-6 h-6 border-2 border-dashed border-gray-400 rounded"></div>
          <span class="text-sm">边框样式</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 这个组件展示了如何在同一个组件中使用两套样式系统
</script>

<style lang="scss" scoped>
/* 现有SCSS样式 */
.tailwind-example {
  max-width: 800px;
  margin: 0 auto;
}

.widget-header {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  font-weight: 600;
}

/* 
 * 这里可以继续使用SCSS，同时Tailwind类也会正常工作
 * 两套系统可以完美共存
 */
</style> 