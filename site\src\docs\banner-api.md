# 轮播图API接口文档

## 概述

本文档描述了首页轮播图和推广图片相关的API接口，包括数据结构、请求方式和响应格式。

## 接口列表

### 1. 获取轮播图列表

**接口地址**: `GET /api/banner/list`

**请求参数**: 无

**响应格式**:
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 1,
      "title": "轮播图标题",
      "subtitle": "轮播图副标题",
      "image": "https://example.com/banner.jpg",
      "link": "/target-page",
      "target": "_self",
      "sort": 100,
      "enabled": true,
      "startTime": "2024-01-01 00:00:00",
      "endTime": "2024-12-31 23:59:59",
      "createTime": "2024-01-01 00:00:00",
      "updateTime": "2024-01-01 00:00:00",
      "clickCount": 156
    }
  ]
}
```

### 2. 获取推广图片列表

**接口地址**: `GET /api/banner/promotions`

**请求参数**: 无

**响应格式**:
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 1,
      "title": "推广标题",
      "subtitle": "推广副标题",
      "image": "https://example.com/promotion.jpg",
      "buttonText": "立即体验",
      "link": "/promotion-page",
      "target": "_self",
      "backgroundColor": "#6C5CE7",
      "textColor": "#FFFFFF",
      "sort": 100,
      "enabled": true,
      "startTime": "2024-01-01 00:00:00",
      "endTime": "2024-12-31 23:59:59",
      "createTime": "2024-01-01 00:00:00",
      "updateTime": "2024-01-01 00:00:00",
      "clickCount": 89
    }
  ]
}
```

### 3. 记录点击统计

**接口地址**: `POST /api/banner/click`

**请求参数**:
```json
{
  "bannerId": 1,
  "clickType": "banner",
  "timestamp": 1704067200000,
  "userAgent": "Mozilla/5.0...",
  "referrer": "https://example.com/previous-page"
}
```

**参数说明**:
- `bannerId`: 轮播图或推广图片ID
- `clickType`: 点击类型，`banner` 或 `promotion`
- `timestamp`: 点击时间戳
- `userAgent`: 用户代理字符串（可选）
- `referrer`: 来源页面（可选）

**响应格式**:
```json
{
  "code": 0,
  "message": "success"
}
```

### 4. 获取统计数据

**接口地址**: `GET /api/banner/stats`

**请求参数**:
- `startDate`: 开始日期（可选），格式：YYYY-MM-DD
- `endDate`: 结束日期（可选），格式：YYYY-MM-DD
- `type`: 统计类型（可选），`banner` 或 `promotion`

**响应格式**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "totalClicks": 245,
    "bannerStats": [
      {
        "id": 1,
        "title": "轮播图标题",
        "clicks": 156,
        "date": "2024-01-01"
      }
    ],
    "promotionStats": [
      {
        "id": 1,
        "title": "推广标题",
        "clicks": 89,
        "date": "2024-01-01"
      }
    ]
  }
}
```

## 数据字段说明

### 轮播图字段(BannerItem)

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 轮播图唯一ID |
| title | string | 是 | 轮播图标题 |
| subtitle | string | 否 | 轮播图副标题 |
| image | string | 是 | 图片URL地址 |
| link | string | 否 | 点击跳转链接 |
| target | string | 否 | 链接打开方式：`_self` 或 `_blank` |
| sort | number | 否 | 排序权重，数字越大越靠前 |
| enabled | boolean | 否 | 是否启用，默认true |
| startTime | string | 否 | 开始展示时间 |
| endTime | string | 否 | 结束展示时间 |
| createTime | string | 否 | 创建时间 |
| updateTime | string | 否 | 更新时间 |
| clickCount | number | 否 | 点击次数统计 |

### 推广图片字段(PromotionItem)

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 推广图片唯一ID |
| title | string | 是 | 推广标题 |
| subtitle | string | 否 | 推广副标题 |
| image | string | 是 | 图片URL地址 |
| buttonText | string | 是 | 按钮文字 |
| link | string | 否 | 点击跳转链接 |
| target | string | 否 | 链接打开方式 |
| backgroundColor | string | 否 | 背景颜色 |
| textColor | string | 否 | 文字颜色 |
| sort | number | 否 | 排序权重 |
| enabled | boolean | 否 | 是否启用 |
| startTime | string | 否 | 开始展示时间 |
| endTime | string | 否 | 结束展示时间 |
| createTime | string | 否 | 创建时间 |
| updateTime | string | 否 | 更新时间 |
| clickCount | number | 否 | 点击次数统计 |

## 响应状态码

| 状态码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1 | 一般错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 错误处理

前端已实现完整的错误处理机制：

1. **网络错误**: 自动重试机制，最多重试3次
2. **请求超时**: 默认10秒超时，可配置
3. **数据异常**: 使用默认数据保证页面正常显示
4. **点击统计失败**: 不影响用户体验，只记录日志

## 配置说明

相关配置在 `site/src/config/api.ts` 文件中：

```typescript
export const BANNER_CONFIG = {
  AUTOPLAY_INTERVAL: 5000,        // 自动播放间隔
  REQUEST_TIMEOUT: 10000,         // 请求超时时间
  MAX_RETRY_COUNT: 3,             // 最大重试次数
  ENABLE_CLICK_TRACKING: true,    // 是否启用点击统计
  ENABLE_ERROR_REPORTING: true    // 是否启用错误上报
}
```

## 缓存策略

- 轮播图数据：缓存5分钟
- 推广图片数据：缓存10分钟
- 点击统计：实时发送，不缓存

## 安全考虑

1. 所有图片URL需要验证有效性
2. 跳转链接需要进行安全检查
3. 点击统计包含用户代理信息，注意隐私保护
4. 建议对API进行频率限制

## 后台管理建议

建议后台管理系统实现以下功能：

1. **轮播图管理**: 增删改查、排序、启用/禁用
2. **推广图片管理**: 增删改查、排序、启用/禁用
3. **统计报表**: 点击数据分析、热门内容排行
4. **时间控制**: 设置展示时间范围
5. **图片管理**: 图片上传、压缩、CDN配置
6. **预览功能**: 实时预览展示效果 