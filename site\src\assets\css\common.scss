// 全局字体大小优化 - 提升阅读体验
html {
  font-size: 16px; // 基础字体从默认的14px增加到16px
}

body {
  font-size: 15px;
  line-height: 1.6;
  letter-spacing: 0.02em; // 增加字符间距，提升可读性
}

// 针对不同内容区域的字体优化
.container {
  font-size: 15px;
  line-height: 1.6;
}

// 导航菜单字体大小
.navbar-item {
  font-size: 16px !important; // 从15px增加到16px
}

// 内容区域字体优化
.content, .main-content, .widget {
  font-size: 15px;
  line-height: 1.6;
  
  p {
    font-size: 15px;
    line-height: 1.7;
    margin-bottom: 1em;
  }
  
  h1, h2, h3, h4, h5, h6 {
    line-height: 1.4;
  }
}

// 卡片内容字体优化
.card, .box {
  font-size: 15px;
  line-height: 1.6;
}

// 按钮字体大小
.button {
  font-size: 14px;
}

// 小字体内容(如时间、标签等)保持适中大小
.is-size-7, .tag, .breadcrumb {
  font-size: 13px !important;
}

.main {
  padding: 1rem 1rem;

  @media screen and (max-width: 768px) {
    & {
      padding: 1rem 0.5rem 0 !important;
    }
  }
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.EASE {
  transition: all 0.2s ease-out 0.1s;
}

.main-body,
.main-aside {
  background: var(--bg-color);
  padding: 10px;

  &.no-bg {
    background: none;
  }
}

// main-container
$sidebar-margin: 10px; // 侧边栏之间的缝隙
$sidebar-width: null; // 侧边栏宽度
$main-container-width: null; // 主栏目的宽度

// 计算页面宽度
@mixin main-size($side-width) {
  // 侧边栏宽度
  $sidebar-width: $side-width;
  // 主栏目的宽度
  $main-container-width: calc(100% - #{$side-width} - #{$sidebar-margin});

  &.left-main {
    .left-container {
      width: $main-container-width;
    }

    .right-container {
      min-width: $sidebar-width;
      max-width: $sidebar-width;
    }
  }

  &.right-main {
    .left-container {
      min-width: $sidebar-width;
      max-width: $sidebar-width;
    }

    .right-container {
      width: $main-container-width;
    }
  }
}

.main-container {
  display: flex;

  // 默认宽度260
  @include main-size(260px);

  // 设定宽度320
  &.size-320 {
    @include main-size(320px);
  }

  // 设定宽度360
  &.size-360 {
    @include main-size(360px);
  }

  .main-content {
    background-color: var(--bg-color);
    padding: 0 10px 10px 10px;
    margin-bottom: 10px;

    &.no-bg {
      background: none;
    }

    .main-content-footer {
      border-top: 1px solid var(--border-color);
      background: var(--bg-color2);
      padding: 5px 15px 5px 15px;
      margin: 0 -10px -10px -10px;
    }

    &.no-padding {
      padding: 0;

      .main-content-footer {
        margin: 0;
      }
    }
  }

  .left-container,
  .right-container {
    padding: 0;
    margin: 0;
    height: 100%;
    box-sizing: border-box;
    display: block;
  }

  &.is-white.left-main {
    .left-container {
      background: var(--bg-color);
      padding: 0 10px 10px 10px;
      // padding: 0 0 10px 0;
    }
  }

  &.is-white.right-main {
    .right-container {
      background: var(--bg-color);
      padding: 0 10px 10px 10px;
      // padding: 0 0 10px 0;
    }
  }

  // 左侧大，右侧小
  &.left-main {
    @media screen and (max-width: 1024px) {
      .left-container {
        width: 100% !important;
        margin-right: 0 !important;
      }

      .right-container {
        display: none;
      }
    }

    .left-container {
      transition: width 0.2s;
    }

    .right-container {
      padding: 0;
      margin-left: $sidebar-margin;
    }
  }

  // 左侧小，右侧大
  &.right-main {
    @media screen and (max-width: 1024px) {
      .right-container {
        width: 100% !important;
        margin-left: 0 !important;
      }

      .left-container {
        display: none;
      }
    }

    .right-container {
      transition: width 0.2s;
    }

    .left-container {
      padding: 0;
      margin-right: $sidebar-margin;
    }
  }
}

.widget {
  background: var(--bg-color);
  padding: 0 12px;
  margin-top: 10px;
  // border-radius: calc(0.25rem - 1px);
  border-radius: var(--border-radius);

  &:first-child {
    margin: 0;
  }

  &.has-border {
    border: 1px solid var(--border-color);
  }

  &.no-margin {
    margin: 0;
  }

  &.no-bg {
    background: none;
  }

  &>.widget-header {
    padding: 8px 0;
    font-size: 16px;
    font-weight: 700;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;

    .slot {
      float: right;
      font-size: 80%;
      font-weight: 400;

      a {
        color: var(--text-color);

        &:hover {
          color: var(--text-link-hover-color);
        }
      }
    }

    .count {
      color: var(--text-color3);
      margin-left: 8px;
      font-size: 14px;
    }

    .delete {
      float: right;
    }
  }

  &>.widget-content {
    padding: 8px 0;
    word-break: break-all;

    .widget-tips {
      color: var(--text-color4);
      font-size: 14px;
      font-weight: 400;
      text-align: center;
      margin: 10px;
    }
  }

  &>.widget-footer {
    border-top: 1px dashed var(--border-color2);
    padding: 10px 0 10px 0;

    &.is-right {
      text-align: right;
    }
  }
}

.stable {
  .str {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    font-size: 14px;

    &:not(:last-child) {
      border-bottom: 1px solid var(--border-color);
    }

    .slabel {
      color: var(--text-color);
      flex-shrink: 0;
      margin-right: 18px;
    }

    .svalue {
      margin-left: 4px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      word-break: break-all;
      color: var(--text-color);
    }
  }
}

img[lazy="loading"],
img.isLoading {
  background-color: var(--bg-color2);
}

img[lazy="error"] {
  background-color: var(--bg-color2);
}

img[lazy="loaded"],
img.isLoaded {
  /* your style here */
}

.footer {
  background: var(--bg-color);
  padding: 20px 0 20px 0;

  .hide {
    a {
      color: var(--text-color4);

      &:hover {
        color: var(--text-color3);
      }
    }
  }
}

.button.post {
  width: 100%;
}

.loading-animation {
  width: 20px;
  height: 20px;
  display: inline-block;
  // TODO
  color: red;
  vertical-align: middle;
  pointer-events: none;
  position: relative;

  &:before,
  &:after {
    content: "";
    width: inherit;
    height: inherit;
    border-radius: 50%;
    background-color: currentcolor;
    opacity: 0.6;
    position: absolute;
    top: 0;
    left: 0;
    -webkit-animation: loading-animation 2s infinite ease-in-out;
    animation: loading-animation 2s infinite ease-in-out;
  }

  .loading-animation:after {
    -webkit-animation-delay: -1s;
    animation-delay: -1s;
  }

  @-webkit-keyframes loading-animation {

    0%,
    100% {
      -webkit-transform: scale(0);
      transform: scale(0);
    }

    50% {
      -webkit-transform: scale(1);
      transform: scale(1);
    }
  }

  @keyframes loading-animation {

    0%,
    100% {
      -webkit-transform: scale(0);
      transform: scale(0);
    }

    50% {
      -webkit-transform: scale(1);
      transform: scale(1);
    }
  }
}

.breadcrumb.my-breadcrumb {
  padding: 10px 0;
  margin: 0;
  font-weight: 700;
  border-bottom: 1px dashed var(--border-color2);
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(1turn);
  }
}

code {
  .token.number {
    // background-color: none !important;
    // align-items: center;
    // background-color: $background;
    // border-radius: $radius-rounded;
    // display: inline-flex;
    // font-size: $size-medium;
    // height: 2em;
    // // justify-content: center;
    // margin-right: 1.5rem;
    // min-width: 2.5em;
    // padding: 0;
    // text-align: center;
    // vertical-align: top;

    font-size: 1em;
    text-align: left;
    white-space: pre;
    display: inline;
    background-color: rgba(0, 0, 0, 0);
    margin: 0;
    padding: 0;
    height: auto;
    justify-content: normal;
    align-items: normal;
    border-radius: 0;
  }
}

.v-md-editor__preview-wrapper {
  color: #000;
}

.button:not(.is-light) {

  &.is-success,
  &.is-primary,
  &.is-info,
  &.is-warning,
  &.is-danger {
    color: var(--button-text-color) !important;
  }

}