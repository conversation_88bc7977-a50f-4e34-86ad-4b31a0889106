<script setup>
// const configStore = useConfigStore()
useHead({
  title: '关于',
})
</script>

<template>
  <section class="main">
    <div class="container">
      <div class="widget">
        <div class="widget-header">
          关于
        </div>
        <div class="widget-content content">
          <h2>简介</h2>
          <p>
            BBS-GO&nbsp;是一款基于Go语言研发的精美小巧的开源社区论坛系统。初期该项目仅用过学习和交流，开源之后越来越多的小伙伴儿开始喜欢和关注他，这也是我长期升级和维护的动力。
          </p>

          <p>
            为方便大家学习和交流我们在2019年8月使用&nbsp;BBS-GO&nbsp;搭建了&nbsp;<a
              href="https://mlog.club"
              target="_blank"
            >码农俱乐部</a>，欢迎大家前往交流。
          </p>

          <h2>与我联系</h2>
          <p>
            如有任何想法、意见、问题可前往&nbsp;<a
              href="https://mlog.club"
              target="_blank"
            >码农俱乐部</a>&nbsp;交流。
          </p>
          <h2>项目贡献者</h2>
          <p>
            <a
              href="https://github.com/mlogclub/bbs-go/graphs/contributors"
              target="_blank"
            >Contributors</a>
          </p>
        </div>
      </div>
    </div>
  </section>
</template>

<style lang="scss" scoped></style>
