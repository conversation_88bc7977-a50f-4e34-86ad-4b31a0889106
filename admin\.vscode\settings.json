{
  // VUE3项目中禁用vetur
  "vetur.validation.template": false,
  "vetur.validation.script": false,
  "vetur.validation.style": false,

  "prettier.enable": true,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "nuxt.isNuxtApp": false,
  "i18n-ally.localesPaths": [
    "src/locale",
    "src/components/message-box/locale",
    "src/views/login/locale"
  ],
  "stylelint.enable": true,
  "stylelint.validate": ["css", "less", "postcss", "scss", "vue", "sass"],
  "cSpell.enabled": false,
  "cSpell.words": ["arcoblue", "axios", "composables", "Gitee", "vueuse"],
  "typescript.tsdk": "node_modules/typescript/lib",
  "files.eol": "\n",
  "files.insertFinalNewline": true,
  "files.trimTrailingWhitespace": true,
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue"
  ],
  "prettier.singleQuote": true,
  "prettier.semi": true,
  "prettier.tabWidth": 2,
  "prettier.printWidth": 100,
  "prettier.trailingComma": "es5",
  "prettier.endOfLine": "lf"
}
