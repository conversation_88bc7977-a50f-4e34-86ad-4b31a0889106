<!-- 推广图片管理页面 -->
<template>
  <div class="container">
    <Breadcrumb :items="['轮播图管理', '推广图片']" />
    <a-card class="general-card">
      <template #title>
        推广图片列表
      </template>
      <template #extra>
        <a-button type="primary" @click="openAddDialog">
          <template #icon>
            <icon-plus />
          </template>
          添加推广图片
        </a-button>
      </template>
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="formModel"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="title" label="标题">
                  <a-input
                    v-model="formModel.title"
                    placeholder="请输入标题"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="status" label="状态">
                  <a-select
                    v-model="formModel.status"
                    placeholder="请选择状态"
                    allow-clear
                  >
                    <a-option :value="1">启用</a-option>
                    <a-option :value="0">禁用</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-space>
                  <a-button type="primary" @click="search">
                    <template #icon>
                      <icon-search />
                    </template>
                    搜索
                  </a-button>
                  <a-button @click="reset">
                    <template #icon>
                      <icon-refresh />
                    </template>
                    重置
                  </a-button>
                </a-space>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
      </a-row>

      <a-table
        row-key="id"
        :loading="loading"
        :pagination="pagination"
        :columns="columns"
        :data="renderData"
        :bordered="false"
        @page-change="onPageChange"
      >
        <template #imageUrl="{ record }">
          <a-image
            :src="record.imageUrl"
            :preview-visible="false"
            :width="80"
            height="auto"
            alt="推广图片"
          />
        </template>
        <template #status="{ record }">
          <a-tag v-if="record.status === 1" color="green">启用</a-tag>
          <a-tag v-else color="red">禁用</a-tag>
        </template>
        <template #operations="{ record }">
          <a-button
            type="text"
            size="small"
            @click="handleEdit(record)"
          >
            编辑
          </a-button>
          <a-popconfirm
            content="确定要删除吗？"
            @ok="handleDelete(record)"
          >
            <a-button
              type="text"
              size="small"
              status="danger"
            >
              删除
            </a-button>
          </a-popconfirm>
        </template>
      </a-table>

      <a-modal
        v-model:visible="visible"
        :title="isEdit ? '编辑推广图片' : '添加推广图片'"
        @cancel="closeModal"
        @before-ok="handleSubmit"
      >
        <a-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-align="left"
          :label-col-props="{ span: 4 }"
          :wrapper-col-props="{ span: 20 }"
        >
          <a-form-item field="title" label="标题" validate-trigger="blur">
            <a-input v-model="form.title" placeholder="请输入标题" />
          </a-form-item>
          <a-form-item field="subtitle" label="副标题">
            <a-input v-model="form.subtitle" placeholder="请输入副标题" />
          </a-form-item>
          <a-form-item field="imageUrl" label="图片" validate-trigger="blur">
            <ImageUpload v-model="form.imageUrl" />
          </a-form-item>
          <a-form-item field="buttonText" label="按钮文字" validate-trigger="blur">
            <a-input v-model="form.buttonText" placeholder="请输入按钮文字" />
          </a-form-item>
          <a-form-item field="link" label="链接">
            <a-input v-model="form.link" placeholder="请输入链接" />
          </a-form-item>
          <a-form-item field="target" label="打开方式">
            <a-select v-model="form.target" placeholder="请选择打开方式">
              <a-option value="_self">当前窗口</a-option>
              <a-option value="_blank">新窗口</a-option>
            </a-select>
          </a-form-item>
          <a-form-item field="backgroundColor" label="背景色">
            <color-picker v-model="form.backgroundColor" />
          </a-form-item>
          <a-form-item field="textColor" label="文字颜色">
            <color-picker v-model="form.textColor" />
          </a-form-item>
          <a-form-item field="sort" label="排序">
            <a-input-number v-model="form.sort" placeholder="请输入排序值，越大越靠前" />
          </a-form-item>
          <a-form-item field="status" label="状态">
            <a-radio-group v-model="form.status">
              <a-radio :value="1">启用</a-radio>
              <a-radio :value="0">禁用</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item field="dateRange" label="展示时间">
            <a-range-picker
              v-model="dateRange"
              show-time
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
            />
          </a-form-item>
        </a-form>
      </a-modal>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { Message } from '@arco-design/web-vue';
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
import ImageUpload from '@/components/ImageUpload.vue';
import ColorPicker from '@/components/color-picker/index.vue';
import { Promotion, PromotionQueryParams, PromotionListResponse, queryPromotionList, createPromotion, updatePromotion, deletePromotion } from '@/api/promotion';

const router = useRouter();
const formRef = ref();
const visible = ref(false);
const isEdit = ref(false);
const loading = ref(false);
const renderData = ref<Promotion[]>([]);
const dateRange = ref<Date[]>([]);

const formModel = reactive({
  title: '',
  status: undefined as number | undefined,
});

const form = reactive<Promotion>({
  id: undefined,
  title: '',
  subtitle: '',
  imageUrl: '',
  buttonText: '',
  link: '',
  target: '_self',
  backgroundColor: '',
  textColor: '',
  sort: 0,
  status: 1,
  startTime: '',
  endTime: '',
});

const rules = {
  title: [{ required: true, message: '请输入标题' }],
  imageUrl: [{ required: true, message: '请上传图片' }],
  buttonText: [{ required: true, message: '请输入按钮文字' }],
};

const columns = computed<TableColumnData[]>(() => [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 80,
  },
  {
    title: '标题',
    dataIndex: 'title',
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '图片',
    slotName: 'imageUrl',
    width: 100,
  },
  {
    title: '按钮文字',
    dataIndex: 'buttonText',
  },
  {
    title: '链接',
    dataIndex: 'link',
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '排序',
    dataIndex: 'sort',
    width: 80,
  },
  {
    title: '状态',
    slotName: 'status',
    width: 80,
  },
  {
    title: '点击数',
    dataIndex: 'clickCount',
    width: 80,
  },
  {
    title: '操作',
    slotName: 'operations',
    width: 150,
  },
]);

const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
});

onMounted(() => {
  fetchData();
});

const search = () => {
  pagination.current = 1;
  fetchData();
};

const reset = () => {
  formModel.title = '';
  formModel.status = undefined;
  search();
};

const fetchData = async () => {
  try {
    loading.value = true;
    const params: PromotionQueryParams = {
      page: pagination.current,
      limit: pagination.pageSize,
      title: formModel.title,
      status: formModel.status,
    };
    const res = await queryPromotionList(params);
    
    // 检查响应数据是否存在并且格式正确
    if (res && res.results) {
      renderData.value = res.results;
      pagination.total = res.page ? res.page.total : 0;
    } else {
      // 如果响应数据格式不正确，设置为空数组
      renderData.value = [];
      pagination.total = 0;
      console.error('Invalid API response format:', res);
      Message.error('获取数据失败：响应格式不正确');
    }
  } catch (err) {
    renderData.value = [];
    pagination.total = 0;
    console.error(err);
    Message.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

const onPageChange = (current: number) => {
  pagination.current = current;
  fetchData();
};

const openAddDialog = () => {
  isEdit.value = false;
  resetForm();
  visible.value = true;
};

const handleEdit = (record: Promotion) => {
  isEdit.value = true;
  resetForm();
  
  Object.keys(form).forEach(key => {
    if (key in record) {
      const formKey = key as keyof Promotion;
      const recordValue = record[formKey];
      if (recordValue !== undefined) {
        (form as any)[formKey] = recordValue;
      }
    }
  });
  
  // 设置时间范围
  if (record.startTime && record.endTime) {
    dateRange.value = [new Date(record.startTime), new Date(record.endTime)];
  } else {
    dateRange.value = [];
  }
  
  visible.value = true;
};

const resetForm = () => {
  form.id = undefined;
  form.title = '';
  form.subtitle = '';
  form.imageUrl = '';
  form.buttonText = '';
  form.link = '';
  form.target = '_self';
  form.backgroundColor = '';
  form.textColor = '';
  form.sort = 0;
  form.status = 1;
  form.startTime = '';
  form.endTime = '';
  dateRange.value = [];
};

const closeModal = () => {
  visible.value = false;
  resetForm();
};

const handleSubmit = async (done: (closed: boolean) => void) => {
  formRef.value.validate().then(async (errors: any) => {
    if (!errors) {
      try {
        // 处理时间范围
        if (dateRange.value && dateRange.value.length === 2) {
          // 检查并确保日期值是Date对象，否则进行转换
          const startDate = dateRange.value[0] instanceof Date 
            ? dateRange.value[0] 
            : new Date(dateRange.value[0]);
          
          const endDate = dateRange.value[1] instanceof Date 
            ? dateRange.value[1] 
            : new Date(dateRange.value[1]);
          
          form.startTime = startDate.toISOString();
          form.endTime = endDate.toISOString();
        } else {
          // 如果没有选择日期范围，使用当前日期作为开始时间，一年后作为结束时间
          const now = new Date();
          const oneYearLater = new Date();
          oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);
          
          form.startTime = now.toISOString();
          form.endTime = oneYearLater.toISOString();
        }
        
        if (isEdit.value) {
          await updatePromotion(form);
          Message.success('更新成功');
        } else {
          await createPromotion(form);
          Message.success('添加成功');
        }
        fetchData();
        done(true);
      } catch (err) {
        console.error(err);
        Message.error('操作失败');
        done(false);
      }
    } else {
      done(false);
    }
  });
};

const handleDelete = async (record: Promotion) => {
  try {
    await deletePromotion({ id: record.id as number });
    Message.success('删除成功');
    fetchData();
  } catch (err) {
    console.error(err);
    Message.error('删除失败');
  }
};
</script>

<style scoped>
.container {
  padding: 0 20px 20px 20px;
}

:deep(.arco-table-th) {
  background-color: var(--color-neutral-2);
}

:deep(.general-card) {
  margin-top: 16px;
}
</style> 