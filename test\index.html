<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BBS-GO 技术论坛</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');
        body {
            font-family: 'Noto Sans SC', sans-serif;
        }
        .topic-item:not(:last-child) {
            border-bottom: 1px solid #e5e7eb;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
        <div class="container mx-auto px-4 flex items-center justify-between h-16">
            <!-- 左侧导航 -->
            <div class="flex items-center space-x-8">
                <a href="#" class="flex items-center space-x-2">
                    <img src="./images/logo.svg" alt="Logo" class="h-8 w-8">
                    <span class="font-bold text-xl hidden sm:block">BBS-GO</span>
                </a>
                <nav class="hidden md:flex space-x-6">
                    <a href="#" class="text-gray-900 font-medium">话题</a>
                    <a href="articles.html" class="text-gray-500 hover:text-gray-900">文章</a>
                    <a href="resources.html" class="text-gray-500 hover:text-gray-900">资料下载</a>
                    <a href="#" class="text-gray-500 hover:text-gray-900">BBS-GO</a>
                    <a href="#" class="text-gray-500 hover:text-gray-900">GitHub</a>
                    <a href="#" class="text-gray-500 hover:text-gray-900">Gitee</a>
                </nav>
            </div>

            <!-- 右侧功能区 -->
            <div class="flex items-center space-x-4">
                <!-- 搜索框 -->
                <div class="relative hidden md:block">
                    <input type="text" placeholder="输入你想查找的内容" class="bg-gray-100 rounded-full py-2 px-4 pl-10 w-64 focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white">
                    <img src="./images/search.svg" alt="搜索" class="absolute left-3 top-2.5 h-5 w-5 text-gray-400">
                </div>

                <!-- 发表按钮 -->
                <button class="bg-blue-500 hover:bg-blue-600 text-white rounded-full px-4 py-2 flex items-center">
                    <img src="./images/plus.svg" alt="发表" class="h-5 w-5 mr-1 text-white">
                    <span>发表</span>
                </button>

                <!-- 消息通知 -->
                <a href="#" class="text-gray-500 hover:text-gray-900">
                    <img src="./images/message.svg" alt="消息" class="h-6 w-6">
                </a>

                <!-- 用户头像 -->
                <a href="#" class="flex items-center space-x-2">
                    <img src="https://via.placeholder.com/32" alt="用户头像" class="h-8 w-8 rounded-full">
                    <span class="hidden md:block">春光</span>
                </a>

                <!-- 暗色模式切换 -->
                <button class="text-gray-500 hover:text-gray-900">
                    <img src="./images/moon.svg" alt="暗色模式" class="h-6 w-6">
                </button>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="container mx-auto px-4 py-6 flex flex-col md:flex-row gap-6">
        <!-- 左侧边栏 -->
        <aside class="w-full md:w-64 bg-white rounded-lg shadow-sm p-4">
            <div class="flex flex-col space-y-2">
                <div class="text-lg font-medium mb-2">Now</div>
                
                <a href="#" class="flex items-center space-x-3 p-2 bg-gray-100 rounded-lg">
                    <img src="./images/recommend.svg" alt="推荐" class="h-6 w-6">
                    <span>推荐</span>
                </a>
                
                <a href="#" class="flex items-center space-x-3 p-2 hover:bg-gray-100 rounded-lg">
                    <img src="./images/follow.svg" alt="关注" class="h-6 w-6">
                    <span>关注</span>
                </a>
                
                <a href="#" class="flex items-center space-x-3 p-2 hover:bg-gray-100 rounded-lg">
                    <img src="./images/chat.svg" alt="交流" class="h-6 w-6">
                    <span>交流</span>
                </a>
                
                <a href="#" class="flex items-center space-x-3 p-2 hover:bg-gray-100 rounded-lg">
                    <img src="./images/question.svg" alt="提问" class="h-6 w-6">
                    <span>提问</span>
                </a>
                
                <a href="#" class="flex items-center space-x-3 p-2 hover:bg-gray-100 rounded-lg">
                    <img src="./images/source.svg" alt="开源" class="h-6 w-6">
                    <span>开源</span>
                </a>
                
                <a href="#" class="flex items-center space-x-3 p-2 hover:bg-gray-100 rounded-lg">
                    <img src="./images/fish.svg" alt="摸鱼" class="h-6 w-6">
                    <span>摸鱼</span>
                </a>
                
                <a href="#" class="flex items-center space-x-3 p-2 hover:bg-gray-100 rounded-lg">
                    <img src="./images/gallery.svg" alt="好友图" class="h-6 w-6">
                    <span>好友图</span>
                </a>
                
                <a href="#" class="flex items-center space-x-3 p-2 hover:bg-gray-100 rounded-lg">
                    <img src="./images/feedback.svg" alt="反馈" class="h-6 w-6">
                    <span>反馈</span>
                </a>
            </div>
            
            <!-- 技术分类导航 (新增) -->
            <div class="mt-8">
                <h3 class="text-lg font-medium mb-3">技术分类</h3>
                <div class="flex flex-wrap gap-2">
                    <a href="#" class="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm">前端</a>
                    <a href="#" class="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm">后端</a>
                    <a href="#" class="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm">AI</a>
                    <a href="#" class="px-3 py-1 bg-yellow-100 text-yellow-700 rounded-full text-sm">DevOps</a>
                    <a href="#" class="px-3 py-1 bg-red-100 text-red-700 rounded-full text-sm">移动开发</a>
                    <a href="#" class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">数据库</a>
                    <a href="#" class="px-3 py-1 bg-indigo-100 text-indigo-700 rounded-full text-sm">云原生</a>
                    <a href="#" class="px-3 py-1 bg-pink-100 text-pink-700 rounded-full text-sm">安全</a>
                </div>
            </div>
            
            <!-- 活跃用户排行 (新增) -->
            <div class="mt-8">
                <h3 class="text-lg font-medium mb-3">活跃贡献者</h3>
                <div class="flex flex-col space-y-3">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <img src="https://via.placeholder.com/32" alt="用户头像" class="h-8 w-8 rounded-full">
                            <span class="text-sm">大喵喵酱</span>
                        </div>
                        <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">1024分</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <img src="https://via.placeholder.com/32" alt="用户头像" class="h-8 w-8 rounded-full">
                            <span class="text-sm">ming</span>
                        </div>
                        <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">768分</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <img src="https://via.placeholder.com/32" alt="用户头像" class="h-8 w-8 rounded-full">
                            <span class="text-sm">freepayzz</span>
                        </div>
                        <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">512分</span>
                    </div>
                </div>
            </div>
        </aside>

        <!-- 右侧主内容 -->
        <div class="flex-1">
            <!-- 话题列表 -->
            <div class="bg-white rounded-lg shadow-sm">
                <!-- 话题项 1 -->
                <div class="p-4 topic-item">
                    <div class="flex items-start space-x-3">
                        <img src="https://via.placeholder.com/48" alt="用户头像" class="h-12 w-12 rounded-full">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2">
                                <h3 class="font-medium">大喵喵酱</h3>
                                <span class="text-gray-500 text-sm">发布于2025-04-25 18:16</span>
                                <span class="ml-auto bg-amber-100 text-amber-700 text-xs px-2 py-0.5 rounded">置顶</span>
                            </div>
                            <a href="post-detail.html" class="block hover:text-blue-600">
                                <h2 class="text-lg font-bold mt-1">GO语言社区系统 BBS-GO v4.0.6 发布，支持一键启动+安装引导</h2>
                                <p class="text-gray-600 mt-2">更新说明 新增安装引导；一键安装，鼠标双击即可启动；新增腾讯云图片上传支持；发布地址 GitHub: https://github.com/mlogclub/bbs-go/releases Gitee: https://gitee.com/ml...</p>
                            </a>
                            
                            <div class="flex items-center space-x-4 mt-4">
                                <button class="flex items-center space-x-1 text-gray-500 hover:text-blue-500">
                                    <img src="./images/chat.svg" alt="交流" class="h-5 w-5">
                                    <span>交流</span>
                                </button>
                                
                                <div class="flex items-center space-x-1 text-gray-500">
                                    <img src="./images/thumbs-up.svg" alt="点赞" class="h-5 w-5">
                                    <span>6</span>
                                </div>
                                
                                <a href="post-detail.html" class="flex items-center space-x-1 text-gray-500 hover:text-blue-500">
                                    <img src="./images/comment.svg" alt="评论" class="h-5 w-5">
                                    <span>5</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 话题项 2 -->
                <div class="p-4 topic-item">
                    <div class="flex items-start space-x-3">
                        <img src="https://via.placeholder.com/48" alt="用户头像" class="h-12 w-12 rounded-full">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2">
                                <h3 class="font-medium">大喵喵酱</h3>
                                <span class="text-gray-500 text-sm">发布于2025-03-04 14:59</span>
                                <span class="ml-auto bg-amber-100 text-amber-700 text-xs px-2 py-0.5 rounded">置顶</span>
                            </div>
                            <a href="post-detail.html" class="block hover:text-blue-600">
                                <h2 class="text-lg font-bold mt-1">GO语言社区系统 BBS-GO v4.0.5版本更新，提供全新的UI</h2>
                                <p class="text-gray-600 mt-2">更新内容 全新的 UI 更细致的权限控制 更便捷的部署方式 发布地址 github: https://github.com/mlogclub/bbs-go/releases/tag/v4.0.5 gitee: https://gitee.com/mlo...</p>
                            </a>
                            
                            <div class="flex items-center space-x-4 mt-4">
                                <button class="flex items-center space-x-1 text-gray-500 hover:text-blue-500">
                                    <img src="./images/source.svg" alt="开源" class="h-5 w-5">
                                    <span>开源</span>
                                </button>
                                
                                <div class="flex items-center space-x-1 text-gray-500">
                                    <img src="./images/thumbs-up.svg" alt="点赞" class="h-5 w-5">
                                    <span>20</span>
                                </div>
                                
                                <a href="post-detail.html" class="flex items-center space-x-1 text-gray-500 hover:text-blue-500">
                                    <img src="./images/comment.svg" alt="评论" class="h-5 w-5">
                                    <span>14</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 话题项 3 -->
                <div class="p-4 topic-item">
                    <div class="flex items-start space-x-3">
                        <img src="https://via.placeholder.com/48" alt="用户头像" class="h-12 w-12 rounded-full">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2">
                                <h3 class="font-medium">ming</h3>
                                <span class="text-gray-500 text-sm">发布于22天前</span>
                            </div>
                            <a href="post-detail.html" class="block hover:text-blue-600">
                                <h2 class="text-lg font-bold mt-1">BBS-GO 安装引导页面数据库连接一直失败。。</h2>
                                <p class="text-gray-600 mt-2">在 vmware 虚拟机里面用 docker compose 跑的 bbs go， BBS-GO 安装引导页面数据库连接一直失败.在 docker-compose 中导出 3306， 虚拟机中使用 mysql 客户端可以连接 mysql， 结果在 BB...</p>
                            </a>
                            
                            <div class="flex items-center space-x-4 mt-4">
                                <button class="flex items-center space-x-1 text-gray-500 hover:text-blue-500">
                                    <img src="./images/chat.svg" alt="交流" class="h-5 w-5">
                                    <span>交流</span>
                                </button>
                                
                                <div class="flex items-center space-x-1 text-gray-500">
                                    <img src="./images/thumbs-up.svg" alt="点赞" class="h-5 w-5">
                                    <span>1</span>
                                </div>
                                
                                <a href="post-detail.html" class="flex items-center space-x-1 text-gray-500 hover:text-blue-500">
                                    <img src="./images/comment.svg" alt="评论" class="h-5 w-5">
                                    <span>3</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 话题项 4 -->
                <div class="p-4 topic-item">
                    <div class="flex items-start space-x-3">
                        <img src="https://via.placeholder.com/48" alt="用户头像" class="h-12 w-12 rounded-full">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2">
                                <h3 class="font-medium">freepayzz</h3>
                                <span class="text-gray-500 text-sm">发布于6天前</span>
                            </div>
                            <a href="post-detail.html" class="block hover:text-blue-600">
                                <h2 class="text-lg font-bold mt-1">便宜的2字符域名</h2>
                                <p class="text-gray-600 mt-2">可以便宜代购 价格3K到2W之间</p>
                            </a>
                            
                            <div class="flex items-center space-x-4 mt-4">
                                <button class="flex items-center space-x-1 text-gray-500 hover:text-blue-500">
                                    <img src="./images/chat.svg" alt="交流" class="h-5 w-5">
                                    <span>交流</span>
                                </button>
                                
                                <div class="flex items-center space-x-1 text-gray-500">
                                    <img src="./images/thumbs-up.svg" alt="点赞" class="h-5 w-5">
                                    <span>0</span>
                                </div>
                                
                                <a href="post-detail.html" class="flex items-center space-x-1 text-gray-500 hover:text-blue-500">
                                    <img src="./images/comment.svg" alt="评论" class="h-5 w-5">
                                    <span>0</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 技术资源库 (新增) -->
            <div class="mt-6 bg-white rounded-lg shadow-sm p-4">
                <h2 class="text-lg font-medium mb-4">技术资源推荐</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <a href="#" class="block p-4 border rounded-lg hover:shadow-md transition-shadow">
                        <h3 class="font-medium text-blue-600">Go语言入门教程</h3>
                        <p class="text-gray-600 text-sm mt-1">从零开始学习Go语言的完整指南</p>
                        <div class="flex items-center mt-2 text-xs text-gray-500">
                            <span>4.8星评分</span>
                            <span class="mx-2">•</span>
                            <span>2.1k阅读</span>
                        </div>
                    </a>
                    <a href="#" class="block p-4 border rounded-lg hover:shadow-md transition-shadow">
                        <h3 class="font-medium text-blue-600">Docker实战指南</h3>
                        <p class="text-gray-600 text-sm mt-1">容器化应用开发与部署最佳实践</p>
                        <div class="flex items-center mt-2 text-xs text-gray-500">
                            <span>4.6星评分</span>
                            <span class="mx-2">•</span>
                            <span>1.8k阅读</span>
                        </div>
                    </a>
                </div>
                <div class="mt-3 text-center">
                    <a href="#" class="text-blue-600 text-sm hover:underline">查看更多资源 →</a>
                </div>
            </div>
            
            <!-- 技术活动日历 (新增) -->
            <div class="mt-6 bg-white rounded-lg shadow-sm p-4">
                <h2 class="text-lg font-medium mb-4">近期技术活动</h2>
                <div class="space-y-3">
                    <div class="flex items-start border-l-4 border-blue-500 pl-3">
                        <div class="mr-3 text-center">
                            <div class="text-lg font-bold">15</div>
                            <div class="text-xs text-gray-500">5月</div>
                        </div>
                        <div>
                            <h3 class="font-medium">Go语言开发者大会</h3>
                            <p class="text-sm text-gray-600 mt-1">线上 • 14:00-18:00</p>
                        </div>
                    </div>
                    <div class="flex items-start border-l-4 border-green-500 pl-3">
                        <div class="mr-3 text-center">
                            <div class="text-lg font-bold">22</div>
                            <div class="text-xs text-gray-500">5月</div>
                        </div>
                        <div>
                            <h3 class="font-medium">云原生技术沙龙</h3>
                            <p class="text-sm text-gray-600 mt-1">北京 • 09:30-17:00</p>
                        </div>
                    </div>
                </div>
                <div class="mt-3 text-center">
                    <a href="#" class="text-blue-600 text-sm hover:underline">查看更多活动 →</a>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t mt-8 py-8">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between">
                <div class="mb-6 md:mb-0">
                    <div class="flex items-center space-x-2">
                        <img src="./images/logo.svg" alt="Logo" class="h-8 w-8">
                        <span class="font-bold text-xl">BBS-GO</span>
                    </div>
                    <p class="text-gray-600 mt-2">基于Go语言开发的现代化社区系统</p>
                </div>
                
                <div class="grid grid-cols-2 md:grid-cols-3 gap-8">
                    <div>
                        <h3 class="font-medium mb-2">产品</h3>
                        <ul class="space-y-2 text-gray-600">
                            <li><a href="#" class="hover:text-gray-900">功能特性</a></li>
                            <li><a href="#" class="hover:text-gray-900">安装指南</a></li>
                            <li><a href="#" class="hover:text-gray-900">API文档</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="font-medium mb-2">资源</h3>
                        <ul class="space-y-2 text-gray-600">
                            <li><a href="#" class="hover:text-gray-900">开发者社区</a></li>
                            <li><a href="#" class="hover:text-gray-900">帮助中心</a></li>
                            <li><a href="#" class="hover:text-gray-900">贡献指南</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="font-medium mb-2">关于</h3>
                        <ul class="space-y-2 text-gray-600">
                            <li><a href="#" class="hover:text-gray-900">关于我们</a></li>
                            <li><a href="#" class="hover:text-gray-900">联系方式</a></li>
                            <li><a href="#" class="hover:text-gray-900">隐私政策</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="border-t mt-8 pt-6 text-center text-gray-600">
                <p>© 2025 BBS-GO. 保留所有权利。</p>
            </div>
        </div>
    </footer>

    <!-- 代码沙箱模态框 (新增) -->
    <div id="codeSandbox" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg w-full max-w-4xl mx-4 p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium">代码沙箱</h3>
                <button onclick="document.getElementById('codeSandbox').classList.add('hidden')" class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="border rounded-lg mb-4">
                <div class="bg-gray-100 px-4 py-2 border-b flex justify-between">
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 bg-blue-500 text-white rounded text-sm">Go</button>
                        <button class="px-3 py-1 hover:bg-gray-200 rounded text-sm">JavaScript</button>
                        <button class="px-3 py-1 hover:bg-gray-200 rounded text-sm">Python</button>
                    </div>
                    <button class="px-3 py-1 bg-green-500 text-white rounded text-sm">运行</button>
                </div>
                <div class="p-4 font-mono text-sm bg-gray-900 text-gray-100 h-64 overflow-auto">
                    <pre>package main

import (
    "fmt"
)

func main() {
    fmt.Println("Hello, BBS-GO!")
}
</pre>
                </div>
            </div>
            <div class="border rounded-lg">
                <div class="bg-gray-100 px-4 py-2 border-b">
                    <span>输出结果</span>
                </div>
                <div class="p-4 font-mono text-sm h-24 overflow-auto">
                    Hello, BBS-GO!
                </div>
            </div>
        </div>
    </div>

    <script>
        // 示例：打开代码沙箱
        function openCodeSandbox() {
            document.getElementById('codeSandbox').classList.remove('hidden');
            document.getElementById('codeSandbox').classList.add('flex');
        }
        
        // 暗色模式切换
        function toggleDarkMode() {
            document.documentElement.classList.toggle('dark');
        }
    </script>
</body>
</html> 