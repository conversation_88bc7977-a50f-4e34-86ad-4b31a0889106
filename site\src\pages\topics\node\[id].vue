<template>
  <section class="main">
    <div class="container main-container size-320">
      <div class="main-content no-padding no-bg topics-wrapper">
        <div class="topics-nav">
          <topics-nav />
        </div>
        <div class="topics-main">
          <!-- 使用新的Tab导航组件 -->
          <topics-tab-nav :current-tab="currentTab" />
          
          <load-more-async
            v-slot="{ results }"
            url="/api/topic/topics"
            :params="{ nodeId: nodeId }"
          >
            <topic-list :topics="results" show-sticky />
          </load-more-async>
        </div>
      </div>
      
      <!-- 右侧边栏 -->
      <div class="right-container">
        <right-sidebar />
      </div>
    </div>
  </section>
</template>

<script setup>
const route = useRoute();

let nodeId = Number.parseInt(route.params.id) || 0;
let nodeName = "";
let currentTab = ref('');

if (route.params.id === "newest") {
  nodeId = 0;
  nodeName = "最新";
  currentTab.value = 'newest';
} else if (route.params.id === "recommend") {
  nodeId = -1;
  nodeName = "推荐";
  currentTab.value = 'recommend';
} else if (route.params.id === "feed") {
  nodeId = -2;
  nodeName = "关注";
  currentTab.value = 'feed';
} else {
  const { data: node } = await useMyFetch(`/api/topic/node?nodeId=${nodeId}`);
  nodeName = node.value.name;
  currentTab.value = '';
}

onMounted(() => {
  useEnvStore().setCurrentNodeId(nodeId);
});

useHead({
  title: useSiteTitle(nodeName, "话题"),
  meta: [
    {
      hid: "description",
      name: "description",
      content: useSiteDescription(),
    },
    { hid: "keywords", name: "keywords", content: useSiteKeywords() },
  ],
});
</script>

<style lang="scss" scoped>
.main-container {
  &.size-320 {
    .right-container {
      min-width: 320px;
      max-width: 320px;
    }
    
    .main-content {
      width: calc(100% - 320px - 10px);
    }
  }
}

.right-container {
  padding-left: 10px;
}

// 响应式设计
@media screen and (max-width: 1024px) {
  .main-container.size-320 {
    .right-container {
      display: none !important;
    }
    
    .main-content {
      width: 100% !important;
    }
  }
}
</style>
