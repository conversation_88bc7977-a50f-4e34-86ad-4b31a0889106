# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
.DS_Store

# Test binary, build with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

bbs-go
bbs-go-linux-*
bbs-go-macos-*
bbs-go-windows-*

bbs-go.yaml
bbs-go.prod.yaml
# bbs-go.dev.yaml
bbs-go.test.yaml

.idea/*
build/*

cscope.files
tags

logs/*.log
web/*
tmp/*
logs/*
site
dist
dist.zip

internal/web*

cmd/generator/*
go.work
go.work.sum

ip2region.xdb

__debug_bin*
