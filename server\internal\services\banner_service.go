package services

import (
	"time"

	"github.com/mlogclub/simple/sqls"
	"github.com/mlogclub/simple/web/params"

	"bbs-go/internal/models"
	"bbs-go/internal/repositories"
)

var BannerService = newBannerService()

func newBannerService() *bannerService {
	return &bannerService{}
}

type bannerService struct {
}

func (s *bannerService) Get(id int64) *models.Banner {
	return repositories.BannerRepository.Get(sqls.DB(), id)
}

func (s *bannerService) Take(where ...interface{}) *models.Banner {
	return repositories.BannerRepository.Take(sqls.DB(), where...)
}

func (s *bannerService) Find(cnd *sqls.Cnd) []models.Banner {
	return repositories.BannerRepository.Find(sqls.DB(), cnd)
}

func (s *bannerService) FindOne(cnd *sqls.Cnd) *models.Banner {
	return repositories.BannerRepository.FindOne(sqls.DB(), cnd)
}

func (s *bannerService) FindPageByParams(params *params.QueryParams) (list []models.Banner, paging *sqls.Paging) {
	return repositories.BannerRepository.FindPageByParams(sqls.DB(), params)
}

func (s *bannerService) FindPageByCnd(cnd *sqls.Cnd) (list []models.Banner, paging *sqls.Paging) {
	return repositories.BannerRepository.FindPageByCnd(sqls.DB(), cnd)
}

func (s *bannerService) Create(t *models.Banner) error {
	t.CreateTime = time.Now()
	t.UpdateTime = time.Now()
	return repositories.BannerRepository.Create(sqls.DB(), t)
}

func (s *bannerService) Update(t *models.Banner) error {
	t.UpdateTime = time.Now()
	return repositories.BannerRepository.Update(sqls.DB(), t)
}

func (s *bannerService) Updates(id int64, columns map[string]interface{}) error {
	return repositories.BannerRepository.Updates(sqls.DB(), id, columns)
}

func (s *bannerService) UpdateColumn(id int64, name string, value interface{}) error {
	return repositories.BannerRepository.UpdateColumn(sqls.DB(), id, name, value)
}

func (s *bannerService) Delete(id int64) {
	repositories.BannerRepository.Delete(sqls.DB(), id)
}

func (s *bannerService) GetActive() []models.Banner {
	return repositories.BannerRepository.FindActive(sqls.DB())
}

// IncrClickCount 增加点击数
func (s *bannerService) IncrClickCount(id int64) {
	sqls.DB().Exec("UPDATE t_banner SET click_count = click_count + 1 WHERE id = ?", id)
}
