export default {
  'login.form.title': 'Login to BBS-GO console',
  'login.form.subtitle': 'Login to BBS-GO console',
  'login.form.userName.errMsg': 'Username cannot be empty',
  'login.form.password.errMsg': 'Password cannot be empty',
  'login.form.captchaCode.errMsg': 'Captcha cannot be empty',
  'login.form.login.errMsg': 'Login error, refresh and try again',
  'login.form.login.success': 'welcome to use',
  'login.form.userName.placeholder': 'Username',
  'login.form.password.placeholder': 'Password',
  'login.form.captchaCode.placeholder': 'Characters in the image',
  'login.form.forgetPassword': 'Forgot password',
  'login.form.login': 'login',
  'login.banner.slogan1': '简洁至上',
  'login.banner.subSlogan1':
    'BBS-GO 的设计理念是简洁至上，注重去除冗余和不必要的复杂性，以提供清晰直观的用户界面和流畅的操作体验。',
  'login.banner.slogan2': '高度可定制',
  'login.banner.subSlogan2':
    '提供清晰的代码结构和模块化的设计，使得用户可以轻松进行二次开发和定制，以满足不同用户群体的需求。',
  'login.banner.slogan3': 'Go语言驱动',
  'login.banner.subSlogan3':
    '使用Go语言开发，充分发挥Go语言高效的并发模型、简单易读的语法以及快速的编译速度，从而提供高性能、稳定的系统。',
};
