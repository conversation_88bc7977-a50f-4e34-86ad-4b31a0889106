<template>
  <div class="right-sidebar">
    <!-- 每日一问 -->
    <div v-if="dailyQuestion" class="widget">
      <div class="widget-header">
        <span class="widget-title">每日一问</span>
      </div>
      <div class="widget-content">
        <div class="daily-question">
          <h3 class="question-title">
            <nuxt-link :to="`/topic/${dailyQuestion.id}`">
              {{ dailyQuestion.title }}
            </nuxt-link>
          </h3>
          <div class="question-meta">
            <span class="author">{{ dailyQuestion.username || '匿名' }}</span>
            <span class="date">{{ dailyQuestion.date || useFormatDate(dailyQuestion.createTime, 'MM-dd') }}</span>
          </div>
          <div class="question-stats">
            <span class="answers">{{ dailyQuestion.answerCount }} 回答</span>
            <span class="views">{{ dailyQuestion.viewCount }} 浏览</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 热门回答 -->
    <div v-if="hotAnswers && hotAnswers.length > 0" class="widget">
      <div class="widget-header">
        <span class="widget-title">热门回答</span>
      </div>
      <div class="widget-content">
        <ul class="hot-answers">
          <li v-for="answer in hotAnswers" :key="answer.id" class="answer-item">
              <div class="answer-content">
              <nuxt-link :to="`/topic/${answer.id}`" class="answer-link">
                {{ answer.title }}
              </nuxt-link>
                </div>
            <div class="answer-meta">
              <span class="author">{{ answer.username || '匿名' }}</span>
              <span class="hot-score">热度 {{ answer.hotScore }}</span>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <my-tags />
    <check-in />
    <score-rank />
    <friend-links />
  </div>
</template>

<script setup>
// 获取每日一问
const { data: dailyQuestion } = await useMyFetch('/api/topic/daily_question');

// 获取热门回答
const { data: hotAnswers } = await useMyFetch('/api/topic/hot_answers', {
  params: { limit: 5 }
});
</script>

<style scoped lang="scss">
.right-sidebar {
  .widget {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.daily-question {
    .question-title {
      font-size: 15px;
      line-height: 1.5;
      color: var(--text-color);
      margin: 0 0 8px 0;
      font-weight: 500;
    
    a {
      color: var(--text-color);
      text-decoration: none;
      
      &:hover {
        color: var(--text-link-color);
      }
    }
    }
    
    .question-meta {
    margin-bottom: 8px;
    
    .author, .date {
        font-size: 13px;
        color: var(--text-color3);
      margin-right: 12px;
  }
}

  .question-stats {
    .answers, .views {
      font-size: 13px;
      color: var(--text-color3);
      margin-right: 12px;
    }
  }
}

.hot-answers {
  list-style: none;
  padding: 0;
  margin: 0;
    
    .answer-item {
      padding: 8px 0;
      border-bottom: 1px solid var(--border-color4);
      
      &:last-child {
        border-bottom: none;
      }
      
      .answer-content {
      margin-bottom: 4px;
        
      .answer-link {
          font-size: 14px;
          line-height: 1.4;
          color: var(--text-color);
          text-decoration: none;
          
          &:hover {
            color: var(--text-link-color);
        }
          }
        }
        
    .answer-meta {
      .author, .hot-score {
          font-size: 12px;
          color: var(--text-color3);
        margin-right: 12px;
        }
    }
  }
}
</style> 