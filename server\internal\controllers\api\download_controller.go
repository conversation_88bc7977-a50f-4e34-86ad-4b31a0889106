package api

import (
	"bbs-go/internal/services"
	"strings"

	"github.com/kataras/iris/v12"
	"github.com/mlogclub/simple/sqls"
	"github.com/mlogclub/simple/web"
	"github.com/mlogclub/simple/web/params"
)

type DownloadController struct {
	Ctx iris.Context
}

// GetList 获取资料列表
func (c *DownloadController) GetList() *web.JsonResult {
	page := params.FormValueIntDefault(c.Ctx, "page", 1)
	limit := params.FormValueIntDefault(c.Ctx, "limit", 20)
	category := params.FormValue(c.Ctx, "category")

	cnd := sqls.NewCnd().Eq("status", 0).Page(page, limit).Desc("id")
	if category != "" && category != "all" {
		cnd.Eq("category", category)
	}

	list, paging := services.DownloadService.FindPageByCnd(cnd)
	return web.JsonPageData(list, paging)
}

// GetDetail 获取资料详情
func (c *DownloadController) GetBy(id int64) *web.JsonResult {
	download := services.DownloadService.Get(id)
	if download == nil || download.Status != 0 {
		return web.JsonErrorMsg("资料不存在")
	}
	return web.JsonData(download)
}

// PostBy 下载资料
func (c *DownloadController) PostBy(id int64) *web.JsonResult {
	download := services.DownloadService.Get(id)
	if download == nil || download.Status != 0 {
		return web.JsonErrorMsg("资料不存在")
	}

	user := services.UserTokenService.GetCurrent(c.Ctx)
	if user != nil {
		// 记录下载日志
		ip := c.Ctx.RemoteAddr()
		if colon := strings.LastIndex(ip, ":"); colon != -1 {
			ip = ip[:colon]
		}
		services.DownloadService.AddDownloadLog(user.Id, id, ip, c.Ctx.GetHeader("User-Agent"))
	}

	// 增加下载次数
	services.DownloadService.IncrDownloadCount(id)

	return web.NewEmptyRspBuilder().Put("url", download.FileUrl).JsonResult()
}

// GetCategories 获取资料分类
func (c *DownloadController) GetCategories() *web.JsonResult {
	categories := services.DownloadService.GetCategories()
	var result []map[string]interface{}

	// 添加"全部"分类
	result = append(result, map[string]interface{}{
		"key":  "all",
		"name": "全部",
	})

	// 添加其他分类
	for _, category := range categories {
		result = append(result, map[string]interface{}{
			"key":  category,
			"name": getCategoryName(category),
		})
	}

	return web.JsonData(result)
}

// <a-option value="">全部</a-option>
// <a-option value="robot">机器人</a-option>
// <a-option value="edge">边缘计算</a-option>
// <a-option value="deep">深度学习</a-option>
// <a-option value="ai">AI算法</a-option>
// <a-option value="other">其他</a-option>
// 获取分类名称
func getCategoryName(key string) string {
	categoryMap := map[string]string{
		"robot": "机器人",
		"edge":  "边缘计算",
		"deep":  "深度学习",
		"ai":    "AI算法",
		"other": "其他",
	}

	if name, ok := categoryMap[key]; ok {
		return name
	}
	return key
}
