/**
 * 问答相关API接口
 */
import { QA_ENDPOINTS, BANNER_CONFIG, API_STATUS, ERROR_MESSAGES } from '~/config/api'

/**
 * 获取每日一问
 * @returns {Promise} 每日一问数据
 */
export function getDailyQuestion() {
  return useFetch(QA_ENDPOINTS.DAILY_QUESTION, {
    method: 'GET',
    timeout: BANNER_CONFIG.REQUEST_TIMEOUT,
    retry: BANNER_CONFIG.MAX_RETRY_COUNT,
    default: () => ({
      errorCode: API_STATUS.SUCCESS,
      message: 'success',
      data: null,
      success: true
    }),
    onRequestError({ error }) {
      console.error('每日一问请求失败:', error)
      throw new Error(ERROR_MESSAGES.NETWORK_ERROR)
    },
    onResponseError({ response }) {
      console.error('每日一问响应错误:', response.status)
      throw new Error(ERROR_MESSAGES.SERVER_ERROR)
    }
  });
}

/**
 * 获取热门回答列表
 * @param {number} limit 限制条数，默认10条
 * @returns {Promise} 热门回答数据
 */
export function getHotAnswers(limit = 10) {
  return useFetch(QA_ENDPOINTS.HOT_ANSWERS, {
    method: 'GET',
    timeout: BANNER_CONFIG.REQUEST_TIMEOUT,
    retry: BANNER_CONFIG.MAX_RETRY_COUNT,
    query: { limit },
    default: () => ({
      errorCode: API_STATUS.SUCCESS,
      message: 'success',
      data: [],
      success: true
    }),
    onRequestError({ error }) {
      console.error('热门回答请求失败:', error)
      throw new Error(ERROR_MESSAGES.NETWORK_ERROR)
    },
    onResponseError({ response }) {
      console.error('热门回答响应错误:', response.status)
      throw new Error(ERROR_MESSAGES.SERVER_ERROR)
    }
  });
}

/**
 * 获取精选问答列表
 * @param {number} limit 限制条数，默认5条
 * @returns {Promise} 精选问答数据
 */
export function getFeaturedQA(limit = 5) {
  return useFetch(QA_ENDPOINTS.FEATURED_QA, {
    method: 'GET',
    timeout: BANNER_CONFIG.REQUEST_TIMEOUT,
    retry: BANNER_CONFIG.MAX_RETRY_COUNT,
    query: { limit },
    default: () => ({
      errorCode: API_STATUS.SUCCESS,
      message: 'success',
      data: [],
      success: true
    }),
    onRequestError({ error }) {
      console.error('精选问答请求失败:', error)
      throw new Error(ERROR_MESSAGES.NETWORK_ERROR)
    },
    onResponseError({ response }) {
      console.error('精选问答响应错误:', response.status)
      throw new Error(ERROR_MESSAGES.SERVER_ERROR)
    }
  });
}

/**
 * 获取七天问答采用榜
 * @param {number} limit 限制条数，默认10条
 * @param {number} days 统计天数，默认7天
 * @returns {Promise} 问答采用榜数据
 */
export function getWeeklyAdoptionRank(limit = 10, days = 7) {
  return useFetch(QA_ENDPOINTS.WEEKLY_ADOPTION_RANK, {
    method: 'GET',
    timeout: BANNER_CONFIG.REQUEST_TIMEOUT,
    retry: BANNER_CONFIG.MAX_RETRY_COUNT,
    query: { limit, days },
    default: () => ({
      errorCode: API_STATUS.SUCCESS,
      message: 'success',
      data: [],
      success: true
    }),
    onRequestError({ error }) {
      console.error('问答采用榜请求失败:', error)
      throw new Error(ERROR_MESSAGES.NETWORK_ERROR)
    },
    onResponseError({ response }) {
      console.error('问答采用榜响应错误:', response.status)
      throw new Error(ERROR_MESSAGES.SERVER_ERROR)
    }
  });
}

/**
 * 获取问答统计数据（管理后台用）
 * @param {Object} params 查询参数
 * @param {string} params.startDate 开始日期
 * @param {string} params.endDate 结束日期
 * @param {string} params.type 统计类型
 * @returns {Promise} 统计数据
 */
export function getQAStats(params = {}) {
  return useFetch(QA_ENDPOINTS.QA_STATS, {
    method: 'GET',
    timeout: BANNER_CONFIG.REQUEST_TIMEOUT,
    query: params,
    default: () => ({
      errorCode: API_STATUS.SUCCESS,
      message: 'success',
      data: {
        totalQuestions: 0,
        totalAnswers: 0,
        featuredCount: 0,
        dailyStats: []
      },
      success: true
    }),
    onRequestError({ error }) {
      console.error('问答统计请求失败:', error)
      throw new Error(ERROR_MESSAGES.NETWORK_ERROR)
    },
    onResponseError({ response }) {
      console.error('问答统计响应错误:', response.status)
      throw new Error(ERROR_MESSAGES.SERVER_ERROR)
    }
  });
} 