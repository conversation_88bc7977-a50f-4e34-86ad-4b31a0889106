<template>
  <div class="home-banner">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-placeholder">
      <div class="carousel-skeleton"></div>
      <div class="bottom-images-skeleton">
        <div class="bottom-image-skeleton" v-for="i in 3" :key="i"></div>
      </div>
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="error" class="error-message">
      <p>{{ error }}</p>
      <button @click="loadData" class="retry-btn">重试</button>
    </div>
    
    <!-- 正常内容 -->
    <template v-else>
      <!-- 轮播图 -->
      <div v-if="carouselSlides.length > 0" class="carousel-wrapper">
      <div class="carousel-container">
        <div class="carousel-slides" :style="{ transform: `translateX(-${currentSlide * 100}%)` }">
          <div 
            v-for="(slide, index) in carouselSlides" 
            :key="slide.id || index"
            class="carousel-slide"
            @click="handleCarouselClick(slide)"
          >
            <img :src="getFullImageUrl(slide.imageUrl || slide.image)" :alt="slide.title" />
            <div class="slide-overlay">
              <div class="slide-content">
                <h3 class="slide-title">{{ slide.title }}</h3>
                <p class="slide-subtitle">{{ slide.subtitle }}</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 轮播图指示器 -->
        <div class="carousel-indicators">
          <button 
            v-for="(slide, index) in carouselSlides" 
            :key="index"
            :class="{ active: currentSlide === index }"
            @click="goToSlide(index)"
          ></button>
        </div>
        
        <!-- 轮播图箭头 -->
        <button class="carousel-btn prev" @click="prevSlide">
          <i class="iconfont icon-left"></i>
        </button>
        <button class="carousel-btn next" @click="nextSlide">
          <i class="iconfont icon-right"></i>
        </button>
      </div>
    </div>
    
      <!-- 下方小图片 -->
      <div v-if="sideImages.length > 0" class="bottom-images">
        <div 
          v-for="(item, index) in sideImages" 
          :key="item.id || index"
          class="bottom-image-item"
          @click="handlePromotionClick(item)"
        >
          <img :src="getFullImageUrl(item.imageUrl || item.image)" :alt="item.title" />
          <div class="bottom-image-overlay">
            <div class="bottom-image-content">
              <h4 class="bottom-image-title">{{ item.title }}</h4>
              <p class="bottom-image-subtitle">{{ item.subtitle }}</p>
              <!-- <div class="bottom-image-button">{{ item.buttonText || '查看详情' }}</div> -->
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { getBannerList, getPromotionList, recordBannerClick } from '~/apis/banner'
import type { BannerItem, PromotionItem, BannerConfig } from '~/types/banner'
import { BANNER_CONFIG, API_STATUS } from '~/config/api'

// 默认配置
const defaultConfig: BannerConfig = {
  autoplayInterval: BANNER_CONFIG.AUTOPLAY_INTERVAL,
  showIndicators: true,
  showArrows: true,
  pauseOnHover: true,
  transitionDuration: BANNER_CONFIG.TRANSITION_DURATION
}

// 响应式数据
const carouselSlides = ref<BannerItem[]>([])
const sideImages = ref<PromotionItem[]>([])
const loading = ref(true)
const error = ref<string | null>(null)

const currentSlide = ref(0);
let autoplayTimer: NodeJS.Timeout | null = null;

// 获取完整图片URL
const getFullImageUrl = (url: string | undefined): string => {
  if (!url) return '';
  
  // 如果已经是完整URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }
  
  // 如果是相对路径，添加基础URL
  const baseUrl = 'http://localhost:8082'; // 根据实际情况修改
  return `${baseUrl}${url.startsWith('/') ? '' : '/'}${url}`;
};

// 处理API响应数据的通用函数
const processApiResponse = (response: any): any[] => {
  if (!response) return [];
  
  // 尝试不同的数据结构路径
  let data = null;
  
  if (response.data?.value) {
    data = response.data.value;
  } else if (response.data) {
    data = response.data;
  } else if (Array.isArray(response)) {
    data = response;
  } else if (response.value) {
    data = response.value;
  }
  
  return Array.isArray(data) ? data : [];
};

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    error.value = null
    
    console.log('开始加载轮播图和推广图片数据...')
    
    // 并行加载轮播图和推广图片数据
    const [bannerResponse, promotionResponse] = await Promise.all([
      getBannerList(),
      getPromotionList()
    ])
    
    console.log('轮播图响应:', bannerResponse)
    console.log('推广图片响应:', promotionResponse)
    
    // 处理轮播图数据
    const bannerData = processApiResponse(bannerResponse);
    console.log('处理后的轮播图数据:', bannerData)
    
    // 处理推广图片数据
    const promotionData = processApiResponse(promotionResponse);
    console.log('处理后的推广图片数据:', promotionData)
    
    // 处理字段映射
    if (bannerData.length > 0) {
      carouselSlides.value = bannerData.map((item: any) => ({
        ...item,
        image: item.imageUrl || item.image,
        enabled: typeof item.status !== 'undefined' ? item.status === 1 : item.enabled
      }));
      console.log('轮播图最终数据:', carouselSlides.value)
    } else {
      console.warn('轮播图数据为空，使用默认数据')
      carouselSlides.value = getDefaultBannerData()
    }
    
    if (promotionData.length > 0) {
      sideImages.value = promotionData.map((item: any) => ({
        ...item,
        image: item.imageUrl || item.image,
        enabled: typeof item.status !== 'undefined' ? item.status === 1 : item.enabled,
        buttonText: item.buttonText || '查看详情'
      }));
      console.log('推广图片最终数据:', sideImages.value)
    } else {
      console.warn('推广图片数据为空，使用默认数据')
      sideImages.value = getDefaultPromotionData()
    }
    
  } catch (err) {
    console.error('数据加载错误:', err)
    error.value = '数据加载失败'
    // 使用默认数据
    carouselSlides.value = getDefaultBannerData()
    sideImages.value = getDefaultPromotionData()
  } finally {
    loading.value = false
  }
}

// 默认轮播图数据（fallback）
const getDefaultBannerData = (): BannerItem[] => [
  {
    id: 1,
    image: 'https://via.placeholder.com/600x200/4A90E2/FFFFFF?text=投稿得积分',
    title: '投稿得积分',
    subtitle: '最高10000积分 超多积分礼品，等你来兑',
    link: '/investment',
    target: '_self',
    enabled: true
  },
  {
    id: 2,
    image: 'https://via.placeholder.com/600x200/50C878/FFFFFF?text=ROS2课程', 
    title: 'ROS2 机器人课程',
    subtitle: '从入门到实战，全面掌握ROS2开发',
    link: '/courses',
    target: '_self',
    enabled: true
  },
  {
    id: 3,
    image: 'https://via.placeholder.com/600x200/FF6B6B/FFFFFF?text=古月居社区',
    title: '古月居社区',
    subtitle: '机器人开发者的交流平台',
    link: '/community',
    target: '_self',
    enabled: true
  }
]

// 默认推广图片数据（fallback）
const getDefaultPromotionData = (): PromotionItem[] => [
  {
    id: 1,
    image: 'https://via.placeholder.com/200x120/4A90E2/FFFFFF?text=机器人教程',
    title: '机器人教程',
    subtitle: '从入门到精通',
    buttonText: '立即学习',
    link: '/tutorials',
    target: '_self',
    enabled: true
  },
  {
    id: 2,
    image: 'https://via.placeholder.com/200x120/50C878/FFFFFF?text=技术问答',
    title: '技术问答',
    subtitle: '解答您的疑惑',
    buttonText: '我要提问',
    link: '/questions',
    target: '_self',
    enabled: true
  },
  {
    id: 3,
    image: 'https://via.placeholder.com/200x120/FF6B6B/FFFFFF?text=开源项目',
    title: '开源项目',
    subtitle: '共建生态',
    buttonText: '查看项目',
    link: '/projects',
    target: '_self',
    enabled: true
  }
]

// 轮播图切换逻辑
const startAutoplay = () => {
  if (autoplayTimer) clearInterval(autoplayTimer)
  
  autoplayTimer = setInterval(() => {
    nextSlide()
  }, defaultConfig.autoplayInterval)
}

const stopAutoplay = () => {
  if (autoplayTimer) {
    clearInterval(autoplayTimer)
    autoplayTimer = null
  }
}

const goToSlide = (index: number) => {
  currentSlide.value = index
  if (defaultConfig.pauseOnHover) {
    stopAutoplay()
    startAutoplay()
  }
}

const nextSlide = () => {
  currentSlide.value = (currentSlide.value + 1) % carouselSlides.value.length
}

const prevSlide = () => {
  currentSlide.value = (currentSlide.value - 1 + carouselSlides.value.length) % carouselSlides.value.length
}

// 处理轮播图点击
const handleCarouselClick = (slide: BannerItem) => {
  if (slide.id) {
    recordBannerClick(slide.id, 'banner')
  }
  
  if (slide.link) {
    window.open(slide.link, slide.target || '_self')
  }
}

// 处理推广图片点击
const handlePromotionClick = (item: PromotionItem) => {
  if (item.id) {
    recordBannerClick(item.id, 'promotion')
  }
  
  if (item.link) {
    window.open(item.link, item.target || '_self')
  }
}

// 初始化数据加载
await loadData()

// 生命周期钩子
onMounted(() => {
  console.log('组件已挂载，重新加载数据以确保客户端显示正确')
  
  // 在客户端挂载时重新加载数据，确保数据正确显示
  loadData().then(() => {
    // 数据加载完成后启动自动播放
    nextTick(() => {
      if (carouselSlides.value.length > 0) {
        console.log('启动轮播图自动播放')
        startAutoplay()
      }
    })
  })
})

onUnmounted(() => {
  console.log('组件卸载，停止自动播放')
  stopAutoplay()
})
</script>

<style lang="scss" scoped>
.home-banner {
  width: 100%;
  margin-bottom: 24px;
}

// 加载状态样式
.loading-placeholder {
  width: 100%;
  
  .carousel-skeleton {
    width: 100%;
    height: 200px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 2s infinite;
    border-radius: 8px;
    margin-bottom: 12px;
  }
  
  .bottom-images-skeleton {
    display: flex;
    gap: 12px;
    
    .bottom-image-skeleton {
      flex: 1;
      height: 120px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: skeleton-loading 2s infinite;
      border-radius: 6px;
    }
  }
  
  @media screen and (max-width: 768px) {
    .carousel-skeleton {
      height: 160px;
    }
    
    .bottom-images-skeleton {
      gap: 8px;
      
      .bottom-image-skeleton {
        height: 100px;
      }
    }
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// 错误状态样式
.error-message {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-color);
  
  p {
    margin: 0 0 16px 0;
    font-size: 14px;
    color: var(--text-color-secondary);
  }
  
  .retry-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    
    &:hover {
      background: var(--primary-color-hover);
    }
  }
}

// 轮播图样式
.carousel-wrapper {
  width: 100%;
  height: 200px; // 与骨架图高度一致
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 12px;
  
  .carousel-container {
    position: relative;
    width: 100%;
    height: 100%;
    
    .carousel-slides {
      display: flex;
      width: 100%;
      height: 100%;
      transition: transform 0.5s ease;
      
      .carousel-slide {
        min-width: 100%;
        height: 100%;
        position: relative;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        
        .slide-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0.1) 100%);
          display: flex;
          align-items: center;
          padding: 24px;
          
          .slide-content {
            .slide-title {
              font-size: 24px;
              font-weight: 700;
              color: white;
              margin: 0 0 8px 0;
              text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            }
            
            .slide-subtitle {
              font-size: 14px;
              color: rgba(255,255,255,0.9);
              margin: 0;
              text-shadow: 0 1px 2px rgba(0,0,0,0.3);
            }
          }
        }
      }
    }
    
    // 轮播图指示器
    .carousel-indicators {
      position: absolute;
      bottom: 16px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      gap: 8px;
      
      button {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        border: none;
        background: rgba(255,255,255,0.5);
        cursor: pointer;
        transition: all 0.3s ease;
        
        &.active {
          background: white;
          transform: scale(1.2);
        }
        
        &:hover {
          background: rgba(255,255,255,0.8);
        }
      }
    }
    
    // 轮播图箭头
    .carousel-btn {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 40px;
      height: 40px;
      border: none;
      border-radius: 50%;
      background: rgba(255,255,255,0.9);
      color: #333;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      opacity: 0;
      
      &:hover {
        background: white;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      }
      
      &.prev {
        left: 16px;
      }
      
      &.next {
        right: 16px;
      }
      
      .iconfont {
        font-size: 18px;
      }
    }
    
    &:hover .carousel-btn {
      opacity: 1;
    }
  }
  
  @media screen and (max-width: 768px) {
    height: 160px; // 与移动端骨架图高度一致
  }
}

// 底部小图片样式
.bottom-images {
  display: flex;
  gap: 12px;
  
  .bottom-image-item {
    flex: 1;
    height: 120px; // 与骨架图高度一致
    position: relative;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .bottom-image-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.1) 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 12px;
      
      .bottom-image-content {
        text-align: center;
        
        .bottom-image-title {
          font-size: 14px;
          font-weight: 600;
          color: white;
          margin: 0 0 4px 0;
          text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }
        
        .bottom-image-subtitle {
          font-size: 11px;
          color: rgba(255,255,255,0.8);
          margin: 0 0 8px 0;
          text-shadow: 0 1px 2px rgba(0,0,0,0.3);
          line-height: 1.3;
          display: none; // 暂时隐藏副标题以节省空间
        }
        
        .bottom-image-button {
          background: rgba(255,255,255,0.9);
          color: #333;
          padding: 4px 12px;
          border-radius: 16px;
          font-size: 12px;
          font-weight: 500;
          display: inline-block;
          transition: all 0.3s ease;
        }
      }
    }
    
    &:hover .bottom-image-button {
      background: white;
      transform: scale(1.05);
    }
  }
  
  @media screen and (max-width: 768px) {
    gap: 8px;
    
    .bottom-image-item {
      height: 100px; // 与移动端骨架图高度一致
      
      .bottom-image-content {
        .bottom-image-title {
          font-size: 12px;
        }
        
        .bottom-image-button {
          font-size: 11px;
          padding: 3px 10px;
        }
      }
    }
  }
}
</style> 