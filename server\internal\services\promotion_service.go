package services

import (
	"time"

	"github.com/mlogclub/simple/sqls"
	"github.com/mlogclub/simple/web/params"

	"bbs-go/internal/models"
	"bbs-go/internal/repositories"
)

var PromotionService = newPromotionService()

func newPromotionService() *promotionService {
	return &promotionService{}
}

type promotionService struct {
}

func (s *promotionService) Get(id int64) *models.Promotion {
	return repositories.PromotionRepository.Get(sqls.DB(), id)
}

func (s *promotionService) Take(where ...interface{}) *models.Promotion {
	return repositories.PromotionRepository.Take(sqls.DB(), where...)
}

func (s *promotionService) Find(cnd *sqls.Cnd) []models.Promotion {
	return repositories.PromotionRepository.Find(sqls.DB(), cnd)
}

func (s *promotionService) FindOne(cnd *sqls.Cnd) *models.Promotion {
	return repositories.PromotionRepository.FindOne(sqls.DB(), cnd)
}

func (s *promotionService) FindPageByParams(params *params.QueryParams) (list []models.Promotion, paging *sqls.Paging) {
	return repositories.PromotionRepository.FindPageByParams(sqls.DB(), params)
}

func (s *promotionService) FindPageByCnd(cnd *sqls.Cnd) (list []models.Promotion, paging *sqls.Paging) {
	return repositories.PromotionRepository.FindPageByCnd(sqls.DB(), cnd)
}

func (s *promotionService) Create(t *models.Promotion) error {
	t.CreateTime = time.Now()
	t.UpdateTime = time.Now()
	return repositories.PromotionRepository.Create(sqls.DB(), t)
}

func (s *promotionService) Update(t *models.Promotion) error {
	t.UpdateTime = time.Now()
	return repositories.PromotionRepository.Update(sqls.DB(), t)
}

func (s *promotionService) Updates(id int64, columns map[string]interface{}) error {
	return repositories.PromotionRepository.Updates(sqls.DB(), id, columns)
}

func (s *promotionService) UpdateColumn(id int64, name string, value interface{}) error {
	return repositories.PromotionRepository.UpdateColumn(sqls.DB(), id, name, value)
}

func (s *promotionService) Delete(id int64) {
	repositories.PromotionRepository.Delete(sqls.DB(), id)
}

func (s *promotionService) GetActive() []models.Promotion {
	return repositories.PromotionRepository.FindActive(sqls.DB())
}

// IncrClickCount 增加点击数
func (s *promotionService) IncrClickCount(id int64) {
	sqls.DB().Exec("UPDATE t_promotion SET click_count = click_count + 1 WHERE id = ?", id)
}
