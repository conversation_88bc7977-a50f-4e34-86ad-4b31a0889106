name: bbs-go-site

on: [ push ]

jobs:
  build:

    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [ 20.x ]

    steps:
      - uses: actions/checkout@v3
      - uses: pnpm/action-setup@v3
        with:
          version: 8
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: "pnpm"
          cache-dependency-path: site/pnpm-lock.yaml
      - name: Install dependencies
        working-directory: ./site
        run: |
          pnpm install
      - name: Build
        working-directory: ./site
        run: |
          pnpm build