# BBS-GO 技术论坛系统

BBS-GO 是一个基于 Go 语言开发的现代化技术论坛系统，采用前后端分离架构，提供完整的论坛功能和管理后台。

## 项目架构

### 整体架构
BBS-GO 采用微服务架构，由以下几个核心服务组成：

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端网站       │    │   管理后台       │    │   后端API服务    │
│   (site)        │    │   (admin)       │    │   (server)      │
│   Nuxt.js 3     │    │   Vue.js 3      │    │   Go + Gin      │
│   Port: 3000    │    │   静态文件       │    │   Port: 8082    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   MySQL 数据库   │
                    │   Port: 3306    │
                    └─────────────────┘
```

## 服务组成详解

### 1. 后端API服务 (server)
**技术栈**: Go 1.24 + Gin + GORM
**端口**: 8082
**作用**:
- 提供所有业务逻辑的REST API接口
- 用户认证与授权管理
- 内容管理（话题、文章、评论等）
- 文件上传处理
- 搜索功能
- 邮件通知服务
- SEO优化功能

**核心模块**:
- `controllers`: 控制器层，处理HTTP请求
  - `api`: 前端API接口
  - `admin`: 管理后台API接口
  - `render`: 页面渲染相关
- `services`: 业务逻辑层
- `repositories`: 数据访问层
- `models`: 数据模型定义
- `middleware`: 中间件（认证、安装检查等）
- `cache`: 缓存管理
- `pkg`: 通用工具包

### 2. 前端网站 (site)
**技术栈**: Nuxt.js 3 + Vue.js 3 + Tailwind CSS
**端口**: 3000
**作用**:
- 用户界面展示
- 响应式设计，支持移动端
- 服务端渲染(SSR)支持
- 暗色模式支持
- 技术论坛特色功能

**特色功能**:
- 技术分类导航
- 活跃用户排行榜
- 技术资源推荐
- 技术活动日历
- 代码沙箱功能

### 3. 管理后台 (admin)
**技术栈**: Vue.js 3 + Arco Design + TypeScript
**部署方式**: 静态文件，集成到后端服务
**作用**:
- 系统管理界面
- 用户管理
- 内容审核
- 系统配置
- 数据统计

### 4. 数据库服务 (MySQL)
**版本**: MySQL 8.0
**端口**: 3306
**作用**:
- 存储所有业务数据
- 用户信息、话题、文章、评论等
- 系统配置信息
- 操作日志

## 服务间交互

### 数据流向
1. **用户访问流程**:
   ```
   用户浏览器 → 前端网站(3000) → 后端API(8082) → MySQL数据库
   ```

2. **管理员操作流程**:
   ```
   管理员浏览器 → 管理后台(静态文件) → 后端API(8082) → MySQL数据库
   ```

### API交互
- 前端网站通过HTTP请求调用后端API获取数据
- 管理后台通过AJAX请求与后端API交互
- 后端API统一处理所有数据操作和业务逻辑

### 认证机制
- 基于Token的用户认证
- JWT令牌管理
- 角色权限控制

## 部署架构

### Docker容器化部署
项目支持Docker容器化部署，通过多阶段构建：

1. **构建阶段**:
   - Go服务构建
   - Nuxt.js前端构建
   - Vue.js管理后台构建

2. **运行阶段**:
   - 单容器运行所有服务
   - 通过start.sh脚本启动多个进程
   - 暴露端口：8082(API) 和 3000(前端)

### 服务启动顺序
```bash
1. MySQL数据库启动
2. 后端API服务启动 (端口8082)
3. 前端网站启动 (端口3000)
4. 管理后台作为静态文件集成到后端服务
```

## 技术特点

### 后端特点
- **高性能**: Go语言原生并发支持
- **模块化**: 清晰的分层架构
- **可扩展**: 插件化的功能模块
- **安全性**: 完善的认证和权限控制

### 前端特点
- **现代化**: 使用最新的前端技术栈
- **响应式**: 适配各种设备屏幕
- **用户体验**: 流畅的交互和加载体验
- **SEO友好**: 服务端渲染支持

### 运维特点
- **容器化**: Docker支持，便于部署
- **监控**: 完善的日志记录
- **配置**: 灵活的配置文件管理
- **扩展**: 支持云存储、邮件服务等

## 开发环境搭建

### 前置要求
- Go 1.24+
- Node.js 20+
- MySQL 8.0+
- Docker (可选)

### 本地开发
1. 启动MySQL数据库
2. 配置server/bbs-go.yaml文件
3. 启动后端服务：`cd server && go run main.go`
4. 启动前端网站：`cd site && pnpm dev`
5. 构建管理后台：`cd admin && pnpm build`

### Docker部署
```bash
docker-compose up -d
```

## 项目特色

BBS-GO 是一个功能完整的技术论坛系统，具有以下特色：

1. **技术导向**: 专为技术社区设计的功能
2. **现代架构**: 前后端分离，微服务设计
3. **高性能**: Go语言后端，响应速度快
4. **易部署**: Docker容器化，一键部署
5. **可定制**: 模块化设计，易于扩展

这个项目适合作为技术社区、企业内部论坛或学习交流平台使用。