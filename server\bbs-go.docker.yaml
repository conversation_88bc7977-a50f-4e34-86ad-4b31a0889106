Port: 8082 # 端口
BaseURL: / # 网站BaseURL
IpDataPath: # IP数据文件，默认读取当前目录下ip2region.xdb文件，最新数据文件请从这里下载：https://github.com/lionsoul2014/ip2region/tree/master/data
AllowedOrigins:
  - "*"

# 日志配置
Logger:
  Filename: /data/logs/bbs-go.log   # 日志文件的位置
  MaxSize: 100                      # 文件最大尺寸（以MB为单位）
  MaxAge: 10                        # 保留旧文件的最大天数
  MaxBackups: 10                    # 保留的最大旧文件数量

# 数据库连接
DB:
  Url: root:123456@tcp(bbs-go-mysql:3306)/bbsgo_db?charset=utf8mb4&parseTime=True&multiStatements=true&loc=Local
  MaxIdleConns: 50
  MaxOpenConns: 200

# 上传配置
Uploader:
  Enable: aliyunOss
  AliyunOss:
    Host:
    Bucket:
    Endpoint:
    AccessId:
    AccessSecret:
    StyleSplitter: "!"
    StyleAvatar: avatar
    StylePreview: preview
    StyleSmall: 768_auto
    StyleDetail: detail
  # 本地文件上传
  Local:
    Host: https://st.mlog.club/ # 上传文件域名
    Path: /data/www/st.mlog.club # 上传目录
  TencentCos:
    Bucket:
    Region:
    SecretId:
    SecretKey:

# 邮件服务器配置，用于邮件通知
Smtp:
  Host:
  Port:
  Username:
  Password:
  SSL: true

# 百度SEO相关配置
# 文档：https://ziyuan.baidu.com/college/courseinfo?id=267&page=2#h2_article_title14
BaiduSEO:
  Site:
  Token:

# 神马搜索SEO相关
# 文档：https://zhanzhang.sm.cn/open/mip
SmSEO:
  Site:
  UserName:
  Token:

# es 配置
Es:
  Url:
  Index:

# 微信配置
WeixinConfig:
  AppID:
  AppSecret:

# search
Search:
  IndexPath: # 索引路径
