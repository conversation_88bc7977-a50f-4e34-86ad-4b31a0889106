module.exports = {
  'extends': [
    'stylelint-config-standard',
    'stylelint-config-recommended-scss',
    'stylelint-config-standard-vue',
  ],
  'defaultSeverity': 'warning',
  'plugins': ['stylelint-order'],
  // 不同格式的文件指定自定义语法
  'overrides': [
    {
      files: ['**/*.(scss|css|vue|html)'],
      customSyntax: 'postcss-scss',
    },
    {
      files: ['**/*.(html|vue)'],
      customSyntax: 'postcss-html',
    },
  ],
  'rules': {
    'at-rule-no-unknown': [
      true,
      {
        ignoreAtRules: ['plugin'],
      },
    ],
    'rule-empty-line-before': [
      'always',
      {
        except: ['after-single-line-comment', 'first-nested'],
      },
    ],
    'selector-pseudo-class-no-unknown': [
      true,
      {
        ignorePseudoClasses: ['deep'],
      },
    ],
  },
  // 指定样式的排序
  'order/properties-order': [
    'position',
    'top',
    'right',
    'bottom',
    'left',
    'z-index',
    'display',
    'justify-content',
    'align-items',
    'flex-shrink',
    'float',
    'clear',
    'overflow',
    'overflow-x',
    'overflow-y',
    'width',
    'min-width',
    'max-width',
    'height',
    'min-height',
    'max-height',
    'padding',
    'padding-top',
    'padding-right',
    'padding-bottom',
    'padding-left',
    'margin',
    'margin-top',
    'margin-right',
    'margin-bottom',
    'margin-left',
    'font-size',
    'font-family',
    'text-align',
    'text-justify',
    'text-indent',
    'text-overflow',
    'text-decoration',
    'white-space',
    'color',
    'background',
    'background-position',
    'background-repeat',
    'background-size',
    'background-color',
    'background-clip',
    'border',
    'border-style',
    'border-width',
    'border-color',
    'border-top-style',
    'border-top-width',
    'border-top-color',
    'border-right-style',
    'border-right-width',
    'border-right-color',
    'border-bottom-style',
    'border-bottom-width',
    'border-bottom-color',
    'border-left-style',
    'border-left-width',
    'border-left-color',
    'border-radius',
    'opacity',
    'filter',
    'list-style',
    'outline',
    'visibility',
    'box-shadow',
    'text-shadow',
    'resize',
    'transition',
    'content',
  ],
};
